/*
 * SPDX-FileCopyrightText: 2024 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#pragma once

#include <stdint.h>

//------------------------------------------- CDC Descriptors ----------------------------------------------------------

/*
    Devices list:
        - USB - UART:
            - FTDI chip
            - TTL232 (FTDI derivative)
            - CP210x
            - CH340
        - USB - Ethernet:
            - ASIX Electronics Corp. AX88772A Fast Ethernet (PremiumCord)
            - ASIX Electronics Corp. AX88772B (i-tec)
            - Realtek Semiconductor Corp. RTL8153 Gigabit Ethernet Adapter (AXAGON)
        - Modems:
            - Qualcomm / Option SimTech SIM7080 (SIM7070G)
            - Quectel Wireless Solutions Co., Ltd. BG96 CAT-M1/NB-IoT modem
            - Qualcomm / Option SimTech, Incorporated (SIM7600E)
            - Qualcomm / Option SimTech SIM7000 (SIM7000E)
            - NOT TESTED: Qualcomm / Option SimTech SIM7080 (SIM7080G)
            - Qualcomm / Option A76XX Series LTE Module (SIMA7672E)
        - USB dongle:
            - NOT TESTED: Shenzhen Rapoo Technology Co., Ltd. Rapoo 2.4G Wireless Device
            - NOT TESTED: Cambridge Silicon Radio, Ltd Bluetooth Dongle (HCI mode)
        - TinyUSB:
            - tusb_composite_msc_serialdevice       esp32s3
            - NOT TESTED: tusb_console              esp32s3
            - NOT TESTED: tusb_hid                  esp32s3
            - NOT TESTED: tusb_midi                 esp32s3
            - NOT TESTED: tusb_msc                  esp32s3
            - NOT TESTED: tusb_ncm                  esp32s3
            - tusb_serial_device (1 x CDC)          esp32s3, esp32p4
            - tusb_serial_device (2 x CDC)          esp32s3, esp32p4
*/

//----------------------------------------------- USB - UART -----------------------------------------------------------

// FTDI chip dual (populated on Wrover-kit)
const uint8_t ftdi_device_desc_fs_hs[] = {
    0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x03, 0x04, 0x10, 0x60, 0x00, 0x07, 0x01, 0x02, 0x00, 0x01
};

const uint8_t ftdi_config_desc_fs[] = {
    0x09, 0x02, 0x37, 0x00, 0x02, 0x01, 0x00, 0x80, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x02, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x02, 0x07, 0x05, 0x83, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x04, 0x02, 0x40, 0x00, 0x00
};

const uint8_t ftdi_config_desc_hs[] = {
    0x09, 0x02, 0x37, 0x00, 0x02, 0x01, 0x00, 0x80, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x02, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x02, 0x07, 0x05, 0x83, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x04, 0x02, 0x00, 0x02, 0x00
};

// TTL232RG (FTDI derivative)
const uint8_t ttl232_device_desc[] = {
    0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x08, 0x03, 0x04, 0x01, 0x60, 0x00, 0x06, 0x01, 0x02, 0x03, 0x01,
};

const uint8_t ttl232_config_desc[] = {
    0x09, 0x02, 0x20, 0x00, 0x01, 0x01, 0x00, 0x80, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x02, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00,
};

// CP210x
// (only FS)
const uint8_t cp210x_device_desc[] = {
    0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0xC4, 0x10, 0x60, 0xEA, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01
};

const uint8_t cp210x_config_desc[] = {
    0x09, 0x02, 0x20, 0x00, 0x01, 0x01, 0x00, 0x80, 0x32, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0x00, 0x00, 0x00, 0x07,
    0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00
};

// CH340
// (only FS)
const uint8_t ch340_device_desc[] = {
    0x12, 0x01, 0x10, 0x01, 0xFF, 0x00, 0x00, 0x08, 0x86, 0x1A, 0x23, 0x75, 0x54, 0x02, 0x00, 0x02, 0x00, 0x01
};

const uint8_t ch340_config_desc[] = {
    0x09, 0x02, 0x27, 0x00, 0x01, 0x01, 0x00, 0x80, 0x30, 0x09, 0x04, 0x00, 0x00, 0x03, 0xFF, 0x01,
    0x02, 0x00, 0x07, 0x05, 0x82, 0x02, 0x20, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x20, 0x00, 0x00,
    0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x01
};

//------------------------------------------- USB - ethernet -----------------------------------------------------------

// ASIX Electronics Corp. AX88772A Fast Ethernet (PremiumCord)
const uint8_t premium_cord_device_desc_fs[] =

{0x12, 0x01, 0x00, 0x02, 0xFF, 0xFF, 0x00, 0x08, 0x95, 0x0B, 0x2A, 0x77, 0x01, 0x00, 0x01, 0x02, 0x03, 0x01};

const uint8_t premium_cord_device_desc_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xFF, 0xFF, 0x00, 0x40, 0x95, 0x0B, 0x2A, 0x77, 0x01, 0x00, 0x01, 0x02, 0x03, 0x01};

const uint8_t premium_cord_config_desc_fs[] =

{
    0x09, 0x02, 0x35, 0x00, 0x01, 0x01, 0x04, 0xA0, 0x7D, 0x09, 0x04, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0x00, 0x07, 0x07,
    0x05, 0x81, 0x03, 0x08, 0x00, 0xA0, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40, 0x00,
    0x00, 0x07, 0x05, 0x84, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x05, 0x02, 0x40, 0x00, 0x00
};

const uint8_t premium_cord_config_desc_hs[] =

{
    0x09, 0x02, 0x35, 0x00, 0x01, 0x01, 0x04, 0xA0, 0x7D, 0x09, 0x04, 0x00, 0x00, 0x05, 0xFF, 0xFF, 0x00, 0x07, 0x07,
    0x05, 0x81, 0x03, 0x08, 0x00, 0x0B, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00, 0x02,
    0x00, 0x07, 0x05, 0x84, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x05, 0x02, 0x00, 0x02, 0x00
};


// ASIX Electronics Corp. AX88772B (i-tec)
const uint8_t i_tec_device_desc_fs[] =

{0x12, 0x01, 0x00, 0x02, 0xFF, 0xFF, 0x00, 0x08, 0x95, 0x0B, 0x2B, 0x77, 0x02, 0x00, 0x01, 0x02, 0x03, 0x01};

const uint8_t i_tec_device_desc_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xFF, 0xFF, 0x00, 0x40, 0x95, 0x0B, 0x2B, 0x77, 0x02, 0x00, 0x01, 0x02, 0x03, 0x01};

const uint8_t i_tec_config_desc_fs[] =

{
    0x09, 0x02, 0x27, 0x00, 0x95, 0x0B, 0x2B, 0x77, 0x02, 0x09, 0x04, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x07, 0x07,
    0x05, 0x81, 0xA0, 0x64, 0x00, 0xA0, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40, 0x00,
    0x00
};

const uint8_t i_tec_config_desc_hs[] =

{
    0x09, 0x02, 0x27, 0x00, 0x01, 0x01, 0x04, 0xA0, 0x64, 0x09, 0x04, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x07, 0x07,
    0x05, 0x81, 0x03, 0x08, 0x00, 0x0B, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00, 0x02,
    0x00
};


// Realtek Semiconductor Corp. RTL8153 Gigabit Ethernet Adapter (AXAGON)
const uint8_t axagon_device_desc_fs_hs[] =

{0x12, 0x01, 0x10, 0x02, 0x00, 0x00, 0x00, 0x40, 0xDA, 0x0B, 0x53, 0x81, 0x00, 0x30, 0x01, 0x02, 0x06, 0x02};

const uint8_t axagon_config_desc_fs_1[] =

{
    0x09, 0x02, 0x27, 0x00, 0x01, 0x01, 0x00, 0xA0, 0x64, 0x09, 0x04, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x83, 0x03, 0x02, 0x00,
    0x08
};

const uint8_t axagon_config_desc_fs_2[] =

{
    0x09, 0x02, 0x50, 0x00, 0x02, 0x02, 0x00, 0xA0, 0x64, 0x09, 0x04, 0x00, 0x00, 0x01, 0x02, 0x06, 0x00, 0x05, 0x05,
    0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x06, 0x00, 0x01, 0x0D, 0x24, 0x0F, 0x03, 0x00, 0x00, 0x00, 0x00, 0xEA, 0x05,
    0x00, 0x00, 0x00, 0x07, 0x05, 0x83, 0x03, 0x10, 0x00, 0x08, 0x09, 0x04, 0x01, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00,
    0x09, 0x04, 0x01, 0x01, 0x02, 0x0A, 0x00, 0x00, 0x04, 0x07, 0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02,
    0x02, 0x40, 0x00, 0x00
};

const uint8_t axagon_config_desc_hs_1[] =

{
    0x09, 0x02, 0x27, 0x00, 0x01, 0x01, 0x00, 0xA0, 0x64, 0x09, 0x04, 0x00, 0x00, 0x03, 0xFF, 0xFF, 0x00, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x83, 0x03, 0x02, 0x00,
    0x08
};

const uint8_t axagon_config_desc_hs_2[] =

{
    0x09, 0x02, 0x50, 0x00, 0x02, 0x02, 0x00, 0xA0, 0x64, 0x09, 0x04, 0x00, 0x00, 0x01, 0x02, 0x06, 0x00, 0x05, 0x05,
    0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x06, 0x00, 0x01, 0x0D, 0x24, 0x0F, 0x03, 0x00, 0x00, 0x00, 0x00, 0xEA, 0x05,
    0x00, 0x00, 0x00, 0x07, 0x05, 0x83, 0x03, 0x10, 0x00, 0x08, 0x09, 0x04, 0x01, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00,
    0x09, 0x04, 0x01, 0x01, 0x02, 0x0A, 0x00, 0x00, 0x04, 0x07, 0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02,
    0x02, 0x00, 0x02, 0x00
};


//------------------------------------------- Modems -------------------------------------------------------------------


// Qualcomm / Option SimTech SIM7080 (SIM7070G)
// lsusb device name: Qualcomm / Option SimTech SIM7080
// IC marking: SIM7070G
const uint8_t sim7070G_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0x02, 0x00, 0x00, 0x40, 0x0E, 0x1E, 0x06, 0x92, 0x00, 0x00, 0x03, 0x02, 0x04, 0x01};

const uint8_t sim7070G_config_desc_fs[] =

{
    0x09, 0x02, 0x9A, 0x00, 0x06, 0x01, 0x01, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x01, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40,
    0x00, 0x00, 0x09, 0x04, 0x03, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x84, 0x02, 0x40, 0x00, 0x00, 0x07,
    0x05, 0x04, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x85, 0x02,
    0x40, 0x00, 0x00, 0x07, 0x05, 0x05, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x05, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00,
    0x07, 0x05, 0x86, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x87, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x06, 0x02, 0x40,
    0x00, 0x00
};

const uint8_t sim7070G_config_desc_hs[] =

{
    0x09, 0x02, 0x9A, 0x00, 0x06, 0x01, 0x01, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x01, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00,
    0x02, 0x00, 0x09, 0x04, 0x03, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x84, 0x02, 0x00, 0x02, 0x00, 0x07,
    0x05, 0x04, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x04, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x85, 0x02,
    0x00, 0x02, 0x00, 0x07, 0x05, 0x05, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x05, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00,
    0x07, 0x05, 0x86, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x87, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x06, 0x02, 0x00,
    0x02, 0x00
};


// Quectel Wireless Solutions Co., Ltd. BG96 CAT-M1/NB-IoT modem
const uint8_t bg96_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x7C, 0x2C, 0x96, 0x02, 0x00, 0x00, 0x03, 0x02, 0x04, 0x01};

const uint8_t bg96_config_desc_fs[] =

{
    0x09, 0x02, 0x91, 0x00, 0x05, 0x01, 0x01, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x01, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x84, 0x02, 0x40,
    0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0xFE, 0xFF, 0x00, 0x07,
    0x05, 0x85, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x86, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x04, 0x02, 0x40, 0x00,
    0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x87, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05,
    0x88, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x05, 0x02, 0x40, 0x00, 0x00
};

const uint8_t bg96_config_desc_hs[] =

{
    0x09, 0x02, 0x91, 0x00, 0x05, 0x01, 0x01, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x01, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x84, 0x02, 0x00,
    0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0xFE, 0xFF, 0x00, 0x07,
    0x05, 0x85, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x86, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x04, 0x02, 0x00, 0x02,
    0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x87, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05,
    0x88, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x05, 0x02, 0x00, 0x02, 0x00
};


// Qualcomm / Option SimTech SIM7000 (SIM7000E)
// lsusb device name: Qualcomm / Option SimTech SIM7000
// IC marking: SIM7000E
const uint8_t sim7000e_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x0E, 0x1E, 0x01, 0x90, 0x00, 0x00, 0x03, 0x02, 0x04, 0x01};

const uint8_t sim7000e_config_desc_fs[] =

{
    0x09, 0x02, 0xA8, 0x00, 0x06, 0x01, 0x01, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x01, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40,
    0x00, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x84, 0x03, 0x40, 0x00, 0x05, 0x07,
    0x05, 0x85, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x04, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF,
    0xFE, 0xFF, 0x00, 0x07, 0x05, 0x86, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x87, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05,
    0x05, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x05, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x88, 0x03, 0x40,
    0x00, 0x05, 0x07, 0x05, 0x89, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x06, 0x02, 0x40, 0x00, 0x00
};

const uint8_t sim7000e_config_desc_hs[] =

{
    0x09, 0x02, 0xA8, 0x00, 0x06, 0x01, 0x01, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x01, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00,
    0x02, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x84, 0x03, 0x40, 0x00, 0x05, 0x07,
    0x05, 0x85, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x04, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF,
    0xFE, 0xFF, 0x00, 0x07, 0x05, 0x86, 0x03, 0x40, 0x00, 0x05, 0x07, 0x05, 0x87, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05,
    0x05, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x05, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x88, 0x03, 0x40,
    0x00, 0x05, 0x07, 0x05, 0x89, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x06, 0x02, 0x00, 0x02, 0x00
};


// Qualcomm / Option SimTech, Incorporated (SIM7600E)
// lsusb device name: Qualcomm / Option SimTech, Incorporated
// IC marking: SIM7600E
const uint8_t sim7600e_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x0E, 0x1E, 0x01, 0x90, 0x18, 0x03, 0x01, 0x02, 0x03, 0x01};

const uint8_t sim7600e_config_desc_fs[] =

{
    0x09, 0x02, 0x02, 0x01, 0x06, 0x01, 0x00, 0xA0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x01, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x01, 0x00, 0x03, 0xFF,
    0x00, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24,
    0x06, 0x00, 0x00, 0x07, 0x05, 0x83, 0x03, 0x0A, 0x00, 0x20, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05,
    0x02, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x02, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01,
    0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x85, 0x03, 0x0A,
    0x00, 0x20, 0x07, 0x05, 0x84, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x03,
    0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02,
    0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x87, 0x03, 0x0A, 0x00, 0x20, 0x07, 0x05, 0x86, 0x02, 0x40, 0x00,
    0x00, 0x07, 0x05, 0x04, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x05, 0x24,
    0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05,
    0x89, 0x03, 0x0A, 0x00, 0x20, 0x07, 0x05, 0x88, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x05, 0x02, 0x40, 0x00, 0x00,
    0x09, 0x04, 0x05, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x8B, 0x03, 0x08, 0x00, 0x20, 0x07, 0x05, 0x8A,
    0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x06, 0x02, 0x40, 0x00, 0x00
};

const uint8_t sim7600e_config_desc_hs[] =

{
    0x09, 0x02, 0x02, 0x01, 0x06, 0x01, 0x00, 0xA0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x01, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x01, 0x00, 0x03, 0xFF,
    0x00, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24,
    0x06, 0x00, 0x00, 0x07, 0x05, 0x83, 0x03, 0x0A, 0x00, 0x09, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05,
    0x02, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x02, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01,
    0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x85, 0x03, 0x0A,
    0x00, 0x09, 0x07, 0x05, 0x84, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x03,
    0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02,
    0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x87, 0x03, 0x0A, 0x00, 0x09, 0x07, 0x05, 0x86, 0x02, 0x00, 0x02,
    0x00, 0x07, 0x05, 0x04, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x00, 0x05, 0x24,
    0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05,
    0x89, 0x03, 0x0A, 0x00, 0x09, 0x07, 0x05, 0x88, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x05, 0x02, 0x00, 0x02, 0x00,
    0x09, 0x04, 0x05, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x8B, 0x03, 0x08, 0x00, 0x09, 0x07, 0x05, 0x8A,
    0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x06, 0x02, 0x00, 0x02, 0x00
};


// Qualcomm / Option SimTech SIM7080 (SIM7080G)
// lsusb device name: Qualcomm / Option SimTech SIM7080
// IC marking SIM7080G
const uint8_t sim7080g_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x0E, 0x1E, 0x05, 0x92, 0x00, 0x00, 0x04, 0x03, 0x05, 0x01};

const uint8_t sim7080g_config_desc_fs[] =

{
    0x09, 0x02, 0xBB, 0x00, 0x06, 0x01, 0x02, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x01, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x03, 0x02, 0x40,
    0x00, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x84, 0x03, 0x40, 0x00, 0x05, 0x07,
    0x05, 0x85, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x04, 0x02, 0x40, 0x00, 0x00, 0x08, 0x0B, 0x04, 0x02, 0x02, 0x00,
    0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x01, 0x02, 0x06, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01, 0x0D, 0x24, 0x0F,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x01, 0x00, 0x00, 0x05, 0x24, 0x06, 0x04, 0x05, 0x07, 0x05, 0x86, 0x03,
    0x40, 0x00, 0x05, 0x09, 0x04, 0x05, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x09, 0x04, 0x05, 0x01, 0x02, 0x0A, 0x00,
    0x00, 0x00, 0x07, 0x05, 0x87, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x05, 0x02, 0x40, 0x00, 0x00
};

const uint8_t sim7080g_config_desc_hs[] =

{
    0x09, 0x02, 0xBB, 0x00, 0x06, 0x01, 0x02, 0xE0, 0xFA, 0x09, 0x04, 0x00, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07,
    0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x01, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x01, 0x00, 0x02, 0xFF,
    0xFF, 0xFF, 0x00, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04,
    0x02, 0x00, 0x02, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x83, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x03, 0x02, 0x00,
    0x02, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0xFF, 0xFF, 0x00, 0x07, 0x05, 0x84, 0x03, 0x40, 0x00, 0x05, 0x07,
    0x05, 0x85, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x04, 0x02, 0x00, 0x02, 0x00, 0x08, 0x0B, 0x04, 0x02, 0x02, 0x00,
    0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x01, 0x02, 0x06, 0x00, 0x00, 0x05, 0x24, 0x00, 0x10, 0x01, 0x0D, 0x24, 0x0F,
    0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x01, 0x00, 0x00, 0x05, 0x24, 0x06, 0x04, 0x05, 0x07, 0x05, 0x86, 0x03,
    0x40, 0x00, 0x05, 0x09, 0x04, 0x05, 0x00, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x09, 0x04, 0x05, 0x01, 0x02, 0x0A, 0x00,
    0x00, 0x00, 0x07, 0x05, 0x87, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x05, 0x02, 0x00, 0x02, 0x00
};


// Qualcomm / Option A76XX Series LTE Module (SIMA7672E)
// lsusb device name: Qualcomm / Option A76XX Series LTE Module
// IC marking SIMA7672E
const uint8_t sima7672e_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x0E, 0x1E, 0x11, 0x90, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t sima7672e_config_desc_fs[] =

{
    0x09, 0x02, 0xF5, 0x00, 0x06, 0x01, 0x00, 0xC0, 0xFA, 0x08, 0x0B, 0x00, 0x02, 0xE0, 0x01, 0x03, 0x05, 0x09, 0x04,
    0x00, 0x00, 0x01, 0xE0, 0x01, 0x03, 0x05, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x00, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x87, 0x03, 0x40, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x05, 0x07, 0x05, 0x83, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x0C, 0x02, 0x40, 0x00, 0x00, 0x09,
    0x04, 0x02, 0x00, 0x02, 0xFF, 0x00, 0x00, 0x08, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x0B, 0x02,
    0x40, 0x00, 0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x0B, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24,
    0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x89, 0x03, 0x40, 0x00, 0x10,
    0x07, 0x05, 0x86, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x0F, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x05, 0x00, 0x03,
    0xFF, 0x00, 0x00, 0x0B, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05,
    0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x88, 0x03, 0x40, 0x00, 0x10, 0x07, 0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x07,
    0x05, 0x0A, 0x02, 0x40, 0x00, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x08, 0x05, 0x24, 0x00, 0x10,
    0x01, 0x05, 0x24, 0x01, 0x00, 0x04, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x03, 0x04, 0x07, 0x05, 0x84, 0x03,
    0x10, 0x00, 0x10, 0x07, 0x05, 0x85, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x0E, 0x02, 0x40, 0x00, 0x00
};

const uint8_t sima7672e_config_desc_hs[] =

{
    0x09, 0x02, 0xF5, 0x00, 0x06, 0x01, 0x00, 0xC0, 0xFA, 0x08, 0x0B, 0x00, 0x02, 0xE0, 0x01, 0x03, 0x05, 0x09, 0x04,
    0x00, 0x00, 0x01, 0xE0, 0x01, 0x03, 0x05, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x00, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x87, 0x03, 0x40, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x05, 0x07, 0x05, 0x83, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x0C, 0x02, 0x00, 0x02, 0x00, 0x09,
    0x04, 0x02, 0x00, 0x02, 0xFF, 0x00, 0x00, 0x08, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x0B, 0x02,
    0x00, 0x02, 0x00, 0x09, 0x04, 0x04, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x0B, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24,
    0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x89, 0x03, 0x40, 0x00, 0x10,
    0x07, 0x05, 0x86, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x0F, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x05, 0x00, 0x03,
    0xFF, 0x00, 0x00, 0x0B, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x01, 0x00, 0x00, 0x04, 0x24, 0x02, 0x02, 0x05,
    0x24, 0x06, 0x00, 0x00, 0x07, 0x05, 0x88, 0x03, 0x40, 0x00, 0x10, 0x07, 0x05, 0x81, 0x02, 0x00, 0x02, 0x00, 0x07,
    0x05, 0x0A, 0x02, 0x00, 0x02, 0x00, 0x09, 0x04, 0x03, 0x00, 0x03, 0xFF, 0x00, 0x00, 0x08, 0x05, 0x24, 0x00, 0x10,
    0x01, 0x05, 0x24, 0x01, 0x00, 0x04, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x03, 0x04, 0x07, 0x05, 0x84, 0x03,
    0x10, 0x00, 0x10, 0x07, 0x05, 0x85, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x0E, 0x02, 0x00, 0x02, 0x00
};

// -------------------------------- USB Dongle -------------------------------------------------------------------------


// Shenzhen Rapoo Technology Co., Ltd. Rapoo 2.4G Wireless Device
// (only FS)
const uint8_t rapoo_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0xAE, 0x24, 0x13, 0x20, 0x10, 0x01, 0x01, 0x02, 0x00, 0x01};

const uint8_t rapoo_config_desc[] =

{
    0x09, 0x02, 0x54, 0x00, 0x03, 0x01, 0x00, 0xA0, 0x32, 0x09, 0x04, 0x00, 0x00, 0x01, 0x03, 0x01, 0x02, 0x00, 0x09,
    0x21, 0x10, 0x01, 0x00, 0x01, 0x22, 0x40, 0x00, 0x07, 0x05, 0x81, 0x03, 0x40, 0x00, 0x0A, 0x09, 0x04, 0x01, 0x00,
    0x01, 0x03, 0x01, 0x01, 0x00, 0x09, 0x21, 0x10, 0x01, 0x00, 0x01, 0x22, 0x5E, 0x00, 0x07, 0x05, 0x82, 0x03, 0x40,
    0x00, 0x0A, 0x09, 0x04, 0x02, 0x00, 0x01, 0x03, 0x01, 0x01, 0x00, 0x09, 0x21, 0x10, 0x01, 0x00, 0x01, 0x22, 0x40,
    0x00, 0x07, 0x05, 0x83, 0x03, 0x40, 0x00, 0x0A
};


// Cambridge Silicon Radio, Ltd Bluetooth Dongle (HCI mode)
const uint8_t csr_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xE0, 0x01, 0x01, 0x40, 0x12, 0x0A, 0x01, 0x00, 0x91, 0x88, 0x00, 0x02, 0x00, 0x01};

const uint8_t csr_config_desc_fs[] =

{
    0x09, 0x02, 0xB1, 0x00, 0x02, 0x01, 0x00, 0xE0, 0x32, 0x09, 0x04, 0x00, 0x00, 0x03, 0xE0, 0x01, 0x01, 0x00, 0x07,
    0x05, 0x81, 0x03, 0x10, 0x00, 0x01, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x01, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00,
    0x01, 0x09, 0x04, 0x01, 0x00, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x00, 0x00, 0x01, 0x07, 0x05,
    0x83, 0x01, 0x00, 0x00, 0x01, 0x09, 0x04, 0x01, 0x01, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x09,
    0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x09, 0x00, 0x01, 0x09, 0x04, 0x01, 0x02, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07,
    0x05, 0x03, 0x01, 0x11, 0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x11, 0x00, 0x01, 0x09, 0x04, 0x01, 0x03, 0x02, 0xE0,
    0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x19, 0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x19, 0x00, 0x01, 0x09, 0x04,
    0x01, 0x04, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x21, 0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x21,
    0x00, 0x01, 0x09, 0x04, 0x01, 0x05, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x31, 0x00, 0x01, 0x07,
    0x05, 0x83, 0x01, 0x31, 0x00, 0x01
};

const uint8_t csr_config_desc_hs[] =

{
    0x09, 0x02, 0xB1, 0x00, 0x02, 0x01, 0x00, 0xE0, 0x32, 0x09, 0x04, 0x00, 0x00, 0x03, 0xE0, 0x01, 0x01, 0x00, 0x07,
    0x05, 0x81, 0x03, 0x10, 0x00, 0x01, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x01, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00,
    0x01, 0x09, 0x04, 0x01, 0x00, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x00, 0x00, 0x01, 0x07, 0x05,
    0x83, 0x01, 0x00, 0x00, 0x01, 0x09, 0x04, 0x01, 0x01, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x09,
    0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x09, 0x00, 0x01, 0x09, 0x04, 0x01, 0x02, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07,
    0x05, 0x03, 0x01, 0x11, 0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x11, 0x00, 0x01, 0x09, 0x04, 0x01, 0x03, 0x02, 0xE0,
    0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x19, 0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x19, 0x00, 0x01, 0x09, 0x04,
    0x01, 0x04, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x21, 0x00, 0x01, 0x07, 0x05, 0x83, 0x01, 0x21,
    0x00, 0x01, 0x09, 0x04, 0x01, 0x05, 0x02, 0xE0, 0x01, 0x01, 0x00, 0x07, 0x05, 0x03, 0x01, 0x31, 0x00, 0x01, 0x07,
    0x05, 0x83, 0x01, 0x31, 0x00, 0x01
};


// ------------------------------------ TinyUSB ------------------------------------------------------------------------


// tusb_composite_msc_serialdevice (dev: esp32s3, host: esp32s3)
const uint8_t tusb_composite_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x3A, 0x30, 0x01, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t tusb_composite_config_desc[] =

{
    0x09, 0x02, 0x62, 0x00, 0x03, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00, 0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x09,
    0x04, 0x02, 0x00, 0x02, 0x08, 0x06, 0x50, 0x05, 0x07, 0x05, 0x03, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x83, 0x02,
    0x40, 0x00, 0x00
};


// tusb_console (dev: esp32s3, host: esp32s3)
const uint8_t tusb_console_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x3A, 0x30, 0x01, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t tusb_console_config_desc[] =

{
    0x09, 0x02, 0x4B, 0x00, 0x02, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00, 0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00
};


// tusb_hid (dev: esp32s3, host: esp32s3)
const uint8_t tusb_hid_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x3A, 0x30, 0x04, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t tusb_hid_config_desc[] =

{
    0x09, 0x02, 0x22, 0x00, 0x01, 0x01, 0x00, 0xA0, 0x32, 0x09, 0x04, 0x00, 0x00, 0x01, 0x03, 0x00, 0x00, 0x04, 0x09,
    0x21, 0x11, 0x01, 0x00, 0x01, 0x22, 0x92, 0x00, 0x07, 0x05, 0x81, 0x03, 0x10, 0x00, 0x0A
};

// tusb_midi (dev: esp32s3, host: esp32s3)
const uint8_t tusb_midi_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x3A, 0x30, 0x08, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t tusb_midi_config_desc[] =

{
    0x09, 0x02, 0x65, 0x00, 0x02, 0x01, 0x00, 0x80, 0x32, 0x09, 0x04, 0x00, 0x00, 0x00, 0x01, 0x01, 0x00, 0x04, 0x09,
    0x24, 0x01, 0x00, 0x01, 0x09, 0x00, 0x01, 0x01, 0x09, 0x04, 0x01, 0x00, 0x02, 0x01, 0x03, 0x00, 0x00, 0x07, 0x24,
    0x01, 0x00, 0x01, 0x41, 0x00, 0x06, 0x24, 0x02, 0x01, 0x01, 0x00, 0x06, 0x24, 0x02, 0x02, 0x02, 0x00, 0x09, 0x24,
    0x03, 0x01, 0x03, 0x01, 0x02, 0x01, 0x00, 0x09, 0x24, 0x03, 0x02, 0x04, 0x01, 0x01, 0x01, 0x00, 0x09, 0x05, 0x01,
    0x02, 0x40, 0x00, 0x00, 0x00, 0x00, 0x05, 0x25, 0x01, 0x01, 0x01, 0x09, 0x05, 0x81, 0x02, 0x40, 0x00, 0x00, 0x00,
    0x00, 0x05, 0x25, 0x01, 0x01, 0x03
};


// tusb_msc (dev: esp32s3, host: esp32s3)
const uint8_t tusb_msc_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x3A, 0x30, 0x02, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t tusb_msc_config_desc[] =

{
    0x09, 0x02, 0x20, 0x00, 0x01, 0x01, 0x00, 0xA0, 0x32, 0x09, 0x04, 0x00, 0x00, 0x02, 0x08, 0x06, 0x50, 0x00, 0x07,
    0x05, 0x01, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x81, 0x02, 0x40, 0x00, 0x00
};


// tusb_ncm (dev: esp32s3, host: esp32s3)
const uint8_t tusb_ncm_device_desc[] =

{0x12, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00, 0x40, 0x3A, 0x30, 0x00, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

const uint8_t tusb_ncm_config_desc[] =

{
    0x09, 0x02, 0x5E, 0x00, 0x02, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x0D, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x0D, 0x00, 0x04, 0x05, 0x24, 0x00, 0x10, 0x01, 0x05, 0x24, 0x06, 0x00, 0x01, 0x0D, 0x24,
    0x0F, 0x05, 0x00, 0x00, 0x00, 0x00, 0xEA, 0x05, 0x00, 0x00, 0x00, 0x06, 0x24, 0x1A, 0x00, 0x01, 0x00, 0x07, 0x05,
    0x81, 0x03, 0x40, 0x00, 0x32, 0x09, 0x04, 0x01, 0x00, 0x00, 0x0A, 0x00, 0x01, 0x00, 0x09, 0x04, 0x01, 0x01, 0x02,
    0x0A, 0x00, 0x01, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00,
};


// tusb_serial_device
const uint8_t tusb_serial_device_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x3A, 0x30, 0x01, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

// (dev: esp32s3, host: esp32s3)
const uint8_t tusb_serial_device_config_desc_fs[] =

{
    0x09, 0x02, 0x4B, 0x00, 0x02, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00, 0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00
};

// (dev: esp32p4, host: esp32p4)
const uint8_t tusb_serial_device_config_desc_hs[] =

{
    0x09, 0x02, 0x4B, 0x00, 0x02, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00, 0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00
};


// tusb_serial_device dual
const uint8_t tusb_serial_device_dual_device_desc_fs_hs[] =

{0x12, 0x01, 0x00, 0x02, 0xEF, 0x02, 0x01, 0x40, 0x3A, 0x30, 0x02, 0x40, 0x00, 0x01, 0x01, 0x02, 0x03, 0x01};

// (dev: esp32s3, host: esp32s3)
const uint8_t tusb_serial_device_dual_config_desc_fs[] =

{
    0x09, 0x02, 0x8D, 0x00, 0x04, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00, 0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x40, 0x00, 0x00, 0x07, 0x05, 0x82, 0x02, 0x40, 0x00, 0x00, 0x08,
    0x0B, 0x02, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04, 0x02, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00,
    0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x03, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x02, 0x03, 0x07, 0x05, 0x83,
    0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x03, 0x00, 0x02, 0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x04, 0x02, 0x40, 0x00,
    0x00, 0x07, 0x05, 0x84, 0x02, 0x40, 0x00, 0x00
};

// (dev: esp32p4, host: esp32p4)
const uint8_t tusb_serial_device_dual_config_desc_hs[] =

{
    0x09, 0x02, 0x8D, 0x00, 0x04, 0x01, 0x00, 0xA0, 0x32, 0x08, 0x0B, 0x00, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04,
    0x00, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00, 0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x01, 0x04, 0x24,
    0x02, 0x02, 0x05, 0x24, 0x06, 0x00, 0x01, 0x07, 0x05, 0x81, 0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x01, 0x00, 0x02,
    0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x02, 0x02, 0x00, 0x02, 0x00, 0x07, 0x05, 0x82, 0x02, 0x00, 0x02, 0x00, 0x08,
    0x0B, 0x02, 0x02, 0x02, 0x02, 0x00, 0x00, 0x09, 0x04, 0x02, 0x00, 0x01, 0x02, 0x02, 0x00, 0x04, 0x05, 0x24, 0x00,
    0x20, 0x01, 0x05, 0x24, 0x01, 0x00, 0x03, 0x04, 0x24, 0x02, 0x02, 0x05, 0x24, 0x06, 0x02, 0x03, 0x07, 0x05, 0x83,
    0x03, 0x08, 0x00, 0x10, 0x09, 0x04, 0x03, 0x00, 0x02, 0x0A, 0x00, 0x00, 0x00, 0x07, 0x05, 0x04, 0x02, 0x00, 0x02,
    0x00, 0x07, 0x05, 0x84, 0x02, 0x00, 0x02, 0x00
};
