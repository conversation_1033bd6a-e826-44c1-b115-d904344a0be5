# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: bootloader
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/

#############################################
# Utility command for menuconfig

build menuconfig: phony CMakeFiles/menuconfig


#############################################
# Utility command for confserver

build confserver: phony CMakeFiles/confserver


#############################################
# Utility command for save-defconfig

build save-defconfig: phony CMakeFiles/save-defconfig


#############################################
# Utility command for gen_project_binary

build gen_project_binary: phony CMakeFiles/gen_project_binary .bin_timestamp bootloader.elf


#############################################
# Utility command for app

build app: phony CMakeFiles/app esp-idf/esptool_py/bootloader_check_size gen_project_binary


#############################################
# Utility command for erase_flash

build erase_flash: phony CMakeFiles/erase_flash


#############################################
# Utility command for monitor

build monitor: phony CMakeFiles/monitor bootloader.elf


#############################################
# Utility command for _project_elf_src

build _project_elf_src: phony CMakeFiles/_project_elf_src project_elf_src_esp32s3.c

# =============================================================================
# Object build statements for EXECUTABLE target bootloader.elf


#############################################
# Order-only phony target for bootloader.elf

build cmake_object_order_depends_target_bootloader.elf: phony || _project_elf_src cmake_object_order_depends_target___idf_main cmake_object_order_depends_target___idf_xtensa project_elf_src_esp32s3.c

build CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj: C_COMPILER__bootloader.2eelf_unscanned_ C$:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/project_elf_src_esp32s3.c || cmake_object_order_depends_target_bootloader.elf
  DEFINES = -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE
  DEP_FILE = CMakeFiles\bootloader.elf.dir\project_elf_src_esp32s3.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always
  INCLUDES = -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include
  OBJECT_DIR = CMakeFiles\bootloader.elf.dir
  OBJECT_FILE_DIR = CMakeFiles\bootloader.elf.dir
  TARGET_COMPILE_PDB = CMakeFiles\bootloader.elf.dir\
  TARGET_PDB = bootloader.elf.pdb


# =============================================================================
# Link build statements for EXECUTABLE target bootloader.elf


#############################################
# Link the executable bootloader.elf

build bootloader.elf: C_EXECUTABLE_LINKER__bootloader.2eelf_ CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj | esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/spi_flash/libspi_flash.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/spi_flash/libspi_flash.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/spi_flash/libspi_flash.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/spi_flash/libspi_flash.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a esp-idf/xtensa/libxtensa.a esp-idf/soc/libsoc.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/hal/libhal.a esp-idf/spi_flash/libspi_flash.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_system/libesp_system.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_common/libesp_common.a esp-idf/esp_rom/libesp_rom.a esp-idf/log/liblog.a C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/libxt_hal.a C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.ld C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.rom.ld || _project_elf_src esp-idf/main/libmain.a esp-idf/xtensa/libxtensa.a
  FLAGS = -mlongcalls
  LINK_FLAGS = -Wl,--cref -Wl,--defsym=IDF_TARGET_ESP32S3=0 -Wl,--Map=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.map -Wl,--no-warn-rwx-segments -fno-rtti -fno-lto -Wl,--gc-sections -Wl,--warn-common -T esp32s3.rom.ld -T esp32s3.rom.api.ld -T esp32s3.rom.bt_funcs.ld -T esp32s3.rom.libgcc.ld -T esp32s3.rom.newlib.ld -T esp32s3.peripherals.ld -T bootloader.ld -T bootloader.rom.ld
  LINK_LIBRARIES = esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/main/libmain.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  esp-idf/xtensa/libxtensa.a  esp-idf/soc/libsoc.a  esp-idf/micro-ecc/libmicro-ecc.a  esp-idf/hal/libhal.a  esp-idf/spi_flash/libspi_flash.a  esp-idf/esp_app_format/libesp_app_format.a  esp-idf/bootloader_support/libbootloader_support.a  esp-idf/efuse/libefuse.a  esp-idf/esp_system/libesp_system.a  esp-idf/esp_hw_support/libesp_hw_support.a  esp-idf/esp_common/libesp_common.a  esp-idf/esp_rom/libesp_rom.a  esp-idf/log/liblog.a  C:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/libxt_hal.a  -u __assert_func  -u abort  -u __ubsan_include  -u bootloader_hooks_include
  LINK_PATH = -LC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3/ld   -LC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/ld   -LC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/main/ld/esp32s3
  OBJECT_DIR = CMakeFiles\bootloader.elf.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\bootloader.elf.dir\
  TARGET_FILE = bootloader.elf
  TARGET_PDB = bootloader.elf.pdb


#############################################
# Utility command for size

build size: phony CMakeFiles/size


#############################################
# Utility command for size-files

build size-files: phony CMakeFiles/size-files


#############################################
# Utility command for size-components

build size-components: phony CMakeFiles/size-components


#############################################
# Utility command for dfu

build dfu: phony CMakeFiles/dfu gen_project_binary


#############################################
# Utility command for dfu-list

build dfu-list: phony CMakeFiles/dfu-list


#############################################
# Utility command for dfu-flash

build dfu-flash: phony CMakeFiles/dfu-flash


#############################################
# Utility command for uf2-app

build uf2-app: phony CMakeFiles/uf2-app gen_project_binary


#############################################
# Utility command for uf2

build uf2: phony CMakeFiles/uf2 gen_project_binary


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Custom command for CMakeFiles\menuconfig

build CMakeFiles/menuconfig | ${cmake_ninja_workdir}CMakeFiles/menuconfig: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe -m kconfgen --list-separator=semicolon --kconfig C:/Users/<USER>/esp/v5.1.6/esp-idf/Kconfig --sdkconfig-rename C:/Users/<USER>/esp/v5.1.6/esp-idf/sdkconfig.rename --config C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env --env IDF_TARGET=esp32s3 --env IDF_ENV_FPGA= --dont-write-deprecated --output config C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/check_term.py && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E env COMPONENT_KCONFIGS_SOURCE_FILE=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/kconfigs.in COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/kconfigs_projbuild.in KCONFIG_CONFIG=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig IDF_TARGET=esp32s3 IDF_ENV_FPGA= c:/Users/<USER>/EspressIF/tools/python_env/idf5.1_py3.11_env/Scripts/python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/menuconfig_wrapper.py C:/Users/<USER>/esp/v5.1.6/esp-idf/Kconfig && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe -m kconfgen --list-separator=semicolon --kconfig C:/Users/<USER>/esp/v5.1.6/esp-idf/Kconfig --sdkconfig-rename C:/Users/<USER>/esp/v5.1.6/esp-idf/sdkconfig.rename --config C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env --env IDF_TARGET=esp32s3 --env IDF_ENV_FPGA= --output config C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig"
  pool = console


#############################################
# Custom command for CMakeFiles\confserver

build CMakeFiles/confserver | ${cmake_ninja_workdir}CMakeFiles/confserver: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe -m kconfserver --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env --kconfig C:/Users/<USER>/esp/v5.1.6/esp-idf/Kconfig --sdkconfig-rename C:/Users/<USER>/esp/v5.1.6/esp-idf/sdkconfig.rename --config C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig"
  pool = console


#############################################
# Custom command for CMakeFiles\save-defconfig

build CMakeFiles/save-defconfig | ${cmake_ninja_workdir}CMakeFiles/save-defconfig: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/prepare_kconfig_files.py --list-separator=semicolon --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe -m kconfgen --list-separator=semicolon --kconfig C:/Users/<USER>/esp/v5.1.6/esp-idf/Kconfig --sdkconfig-rename C:/Users/<USER>/esp/v5.1.6/esp-idf/sdkconfig.rename --config C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig --env-file C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config.env --dont-write-deprecated --output savedefconfig C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/sdkconfig.defaults"
  pool = console


#############################################
# Phony custom command for CMakeFiles\gen_project_binary

build CMakeFiles/gen_project_binary | ${cmake_ninja_workdir}CMakeFiles/gen_project_binary: phony .bin_timestamp || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a


#############################################
# Custom command for .bin_timestamp

build .bin_timestamp | ${cmake_ninja_workdir}.bin_timestamp: CUSTOM_COMMAND bootloader.elf || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/esptool/esptool.py --chip esp32s3 elf2image --flash_mode dio --flash_freq 80m --flash_size 2MB --min-rev-full 0 --max-rev-full 99 -o C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.bin C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.elf && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E echo "Generated C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.bin" && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E md5sum C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.bin > C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/.bin_timestamp"
  DESC = Generating binary image from built executable
  restat = 1


#############################################
# Phony custom command for CMakeFiles\app

build CMakeFiles/app | ${cmake_ninja_workdir}CMakeFiles/app: phony || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/esptool_py/bootloader_check_size esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a gen_project_binary


#############################################
# Custom command for CMakeFiles\erase_flash

build CMakeFiles/erase_flash | ${cmake_ninja_workdir}CMakeFiles/erase_flash: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.1.6\esp-idf\components\esptool_py && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.1.6/esp-idf -D SERIAL_TOOL=c:/Users/<USER>/EspressIF/tools/python_env/idf5.1_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=erase_flash -P run_serial_tool.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\monitor

build CMakeFiles/monitor | ${cmake_ninja_workdir}CMakeFiles/monitor: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.1.6\esp-idf\components\esptool_py && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.1.6/esp-idf -D SERIAL_TOOL=c:/Users/<USER>/EspressIF/tools/python_env/idf5.1_py3.11_env/Scripts/python.exe;-m;esp_idf_monitor -D SERIAL_TOOL_ARGS=--target;esp32s3;--revision;0;C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.elf -D WORKING_DIRECTORY=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader -P run_serial_tool.cmake"
  pool = console


#############################################
# Phony custom command for CMakeFiles\_project_elf_src

build CMakeFiles/_project_elf_src | ${cmake_ninja_workdir}CMakeFiles/_project_elf_src: phony project_elf_src_esp32s3.c


#############################################
# Custom command for project_elf_src_esp32s3.c

build project_elf_src_esp32s3.c | ${cmake_ninja_workdir}project_elf_src_esp32s3.c: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E touch C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/project_elf_src_esp32s3.c"
  DESC = Generating project_elf_src_esp32s3.c
  restat = 1


#############################################
# Custom command for CMakeFiles\size

build CMakeFiles/size | ${cmake_ninja_workdir}CMakeFiles/size: CUSTOM_COMMAND bootloader.map
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D IDF_SIZE_TOOL=c:/Users/<USER>/EspressIF/tools/python_env/idf5.1_py3.11_env/Scripts/python.exe;-m;esp_idf_size -D MAP_FILE=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.map -D OUTPUT_JSON= -P C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/run_size_tool.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\size-files

build CMakeFiles/size-files | ${cmake_ninja_workdir}CMakeFiles/size-files: CUSTOM_COMMAND bootloader.map
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D IDF_SIZE_TOOL=c:/Users/<USER>/EspressIF/tools/python_env/idf5.1_py3.11_env/Scripts/python.exe;-m;esp_idf_size -D IDF_SIZE_MODE=--files -D MAP_FILE=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.map -D OUTPUT_JSON= -P C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/run_size_tool.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\size-components

build CMakeFiles/size-components | ${cmake_ninja_workdir}CMakeFiles/size-components: CUSTOM_COMMAND bootloader.map
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D IDF_SIZE_TOOL=c:/Users/<USER>/EspressIF/tools/python_env/idf5.1_py3.11_env/Scripts/python.exe;-m;esp_idf_size -D IDF_SIZE_MODE=--archives -D MAP_FILE=C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.map -D OUTPUT_JSON= -P C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/run_size_tool.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\dfu

build CMakeFiles/dfu | ${cmake_ninja_workdir}CMakeFiles/dfu: CUSTOM_COMMAND bootloader || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/mkdfu.py write -o C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/dfu.bin --json C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/flasher_args.json --pid 9 --flash-size 2MB"
  pool = console


#############################################
# Custom command for CMakeFiles\dfu-list

build CMakeFiles/dfu-list | ${cmake_ninja_workdir}CMakeFiles/dfu-list: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D ESP_DFU_LIST="1" -P C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/run_dfu_util.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\dfu-flash

build CMakeFiles/dfu-flash | ${cmake_ninja_workdir}CMakeFiles/dfu-flash: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -D ESP_DFU_BIN="C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/dfu.bin" -D ESP_DFU_PID="9" -P C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/run_dfu_util.cmake"
  pool = console


#############################################
# Custom command for CMakeFiles\uf2-app

build CMakeFiles/uf2-app | ${cmake_ninja_workdir}CMakeFiles/uf2-app: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/mkuf2.py write -o C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/uf2-app.bin --json C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/flasher_args.json --chip-id 0xc47e5767 --bin app"
  pool = console


#############################################
# Custom command for CMakeFiles\uf2

build CMakeFiles/uf2 | ${cmake_ninja_workdir}CMakeFiles/uf2: CUSTOM_COMMAND bootloader || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/tools/mkuf2.py write -o C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/uf2.bin --json C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/flasher_args.json --chip-id 0xc47e5767"
  pool = console

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/edit_cache: phony esp-idf/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/rebuild_cache: phony esp-idf/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_xtensa


#############################################
# Order-only phony target for __idf_xtensa

build cmake_object_order_depends_target___idf_xtensa: phony || cmake_object_order_depends_target___idf_soc

build esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj: C_COMPILER____idf_xtensa_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/eri.c || cmake_object_order_depends_target___idf_xtensa
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir\eri.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir
  OBJECT_FILE_DIR = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir
  TARGET_COMPILE_PDB = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir\__idf_xtensa.pdb
  TARGET_PDB = esp-idf\xtensa\libxtensa.pdb

build esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj: C_COMPILER____idf_xtensa_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/xt_trax.c || cmake_object_order_depends_target___idf_xtensa
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir\xt_trax.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir
  OBJECT_FILE_DIR = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir
  TARGET_COMPILE_PDB = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir\__idf_xtensa.pdb
  TARGET_PDB = esp-idf\xtensa\libxtensa.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_xtensa


#############################################
# Link the static library esp-idf\xtensa\libxtensa.a

build esp-idf/xtensa/libxtensa.a: C_STATIC_LIBRARY_LINKER____idf_xtensa_ esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj || esp-idf/soc/libsoc.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\xtensa\CMakeFiles\__idf_xtensa.dir\__idf_xtensa.pdb
  TARGET_FILE = esp-idf\xtensa\libxtensa.a
  TARGET_PDB = esp-idf\xtensa\libxtensa.pdb


#############################################
# Utility command for edit_cache

build esp-idf/xtensa/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\xtensa && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/xtensa/edit_cache: phony esp-idf/xtensa/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/xtensa/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\xtensa && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/xtensa/rebuild_cache: phony esp-idf/xtensa/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/newlib/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\newlib && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/newlib/edit_cache: phony esp-idf/newlib/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/newlib/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\newlib && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/newlib/rebuild_cache: phony esp-idf/newlib/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_soc


#############################################
# Order-only phony target for __idf_soc

build cmake_object_order_depends_target___idf_soc: phony || cmake_object_order_depends_target___idf_micro-ecc

build esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/lldesc.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\lldesc.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/dport_access_common.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\dport_access_common.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/interrupts.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\interrupts.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/gpio_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\gpio_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/uart_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\uart_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/adc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\adc_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/dedic_gpio_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\dedic_gpio_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/gdma_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\gdma_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/spi_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\spi_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/ledc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\ledc_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/pcnt_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\pcnt_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/rmt_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\rmt_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/sdm_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\sdm_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/i2s_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\i2s_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/i2c_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\i2c_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/temperature_sensor_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\temperature_sensor_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/timer_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\timer_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/lcd_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\lcd_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/mcpwm_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\mcpwm_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/sdmmc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\sdmmc_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/touch_sensor_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\touch_sensor_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/twai_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\twai_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/usb_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\usb_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/usb_dwc_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\usb_dwc_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb

build esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj: C_COMPILER____idf_soc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/rtc_io_periph.c || cmake_object_order_depends_target___idf_soc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3\rtc_io_periph.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  OBJECT_FILE_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_PDB = esp-idf\soc\libsoc.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_soc


#############################################
# Link the static library esp-idf\soc\libsoc.a

build esp-idf/soc/libsoc.a: C_STATIC_LIBRARY_LINKER____idf_soc_ esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj || esp-idf/micro-ecc/libmicro-ecc.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\soc\CMakeFiles\__idf_soc.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\soc\CMakeFiles\__idf_soc.dir\__idf_soc.pdb
  TARGET_FILE = esp-idf\soc\libsoc.a
  TARGET_PDB = esp-idf\soc\libsoc.pdb


#############################################
# Utility command for edit_cache

build esp-idf/soc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\soc && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/soc/edit_cache: phony esp-idf/soc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/soc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\soc && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/soc/rebuild_cache: phony esp-idf/soc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_micro-ecc


#############################################
# Order-only phony target for __idf_micro-ecc

build cmake_object_order_depends_target___idf_micro-ecc: phony || cmake_object_order_depends_target___idf_hal

build esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj: C_COMPILER____idf_micro-ecc_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c || cmake_object_order_depends_target___idf_micro-ecc
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir\uECC_verify_antifault.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir
  OBJECT_FILE_DIR = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir
  TARGET_COMPILE_PDB = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir\__idf_micro-ecc.pdb
  TARGET_PDB = esp-idf\micro-ecc\libmicro-ecc.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_micro-ecc


#############################################
# Link the static library esp-idf\micro-ecc\libmicro-ecc.a

build esp-idf/micro-ecc/libmicro-ecc.a: C_STATIC_LIBRARY_LINKER____idf_micro-ecc_ esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj || esp-idf/hal/libhal.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\micro-ecc\CMakeFiles\__idf_micro-ecc.dir\__idf_micro-ecc.pdb
  TARGET_FILE = esp-idf\micro-ecc\libmicro-ecc.a
  TARGET_PDB = esp-idf\micro-ecc\libmicro-ecc.pdb


#############################################
# Utility command for edit_cache

build esp-idf/micro-ecc/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\micro-ecc && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/micro-ecc/edit_cache: phony esp-idf/micro-ecc/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/micro-ecc/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\micro-ecc && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/micro-ecc/rebuild_cache: phony esp-idf/micro-ecc/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_hal


#############################################
# Order-only phony target for __idf_hal

build cmake_object_order_depends_target___idf_hal: phony || cmake_object_order_depends_target___idf_spi_flash

build esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj: C_COMPILER____idf_hal_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/mpu_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\mpu_hal.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb

build esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj: C_COMPILER____idf_hal_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/efuse_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\efuse_hal.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb

build esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj: C_COMPILER____idf_hal_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/efuse_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\esp32s3\efuse_hal.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb

build esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj: C_COMPILER____idf_hal_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/mmu_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\mmu_hal.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb

build esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj: C_COMPILER____idf_hal_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/cache_hal.c || cmake_object_order_depends_target___idf_hal
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\hal\CMakeFiles\__idf_hal.dir\cache_hal.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  OBJECT_FILE_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_PDB = esp-idf\hal\libhal.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_hal


#############################################
# Link the static library esp-idf\hal\libhal.a

build esp-idf/hal/libhal.a: C_STATIC_LIBRARY_LINKER____idf_hal_ esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj || esp-idf/spi_flash/libspi_flash.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\hal\CMakeFiles\__idf_hal.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\hal\CMakeFiles\__idf_hal.dir\__idf_hal.pdb
  TARGET_FILE = esp-idf\hal\libhal.a
  TARGET_PDB = esp-idf\hal\libhal.pdb


#############################################
# Utility command for edit_cache

build esp-idf/hal/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\hal && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/hal/edit_cache: phony esp-idf/hal/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/hal/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\hal && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/hal/rebuild_cache: phony esp-idf/hal/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_spi_flash


#############################################
# Order-only phony target for __idf_spi_flash

build cmake_object_order_depends_target___idf_spi_flash: phony || cmake_object_order_depends_target___idf_esp_app_format

build esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj: C_COMPILER____idf_spi_flash_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/spi_flash_wrap.c || cmake_object_order_depends_target___idf_spi_flash
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\spi_flash_wrap.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include/spi_flash -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir
  OBJECT_FILE_DIR = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir
  TARGET_COMPILE_PDB = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\__idf_spi_flash.pdb
  TARGET_PDB = esp-idf\spi_flash\libspi_flash.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_spi_flash


#############################################
# Link the static library esp-idf\spi_flash\libspi_flash.a

build esp-idf/spi_flash/libspi_flash.a: C_STATIC_LIBRARY_LINKER____idf_spi_flash_ esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj || esp-idf/esp_app_format/libesp_app_format.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\spi_flash\CMakeFiles\__idf_spi_flash.dir\__idf_spi_flash.pdb
  TARGET_FILE = esp-idf\spi_flash\libspi_flash.a
  TARGET_PDB = esp-idf\spi_flash\libspi_flash.pdb


#############################################
# Utility command for edit_cache

build esp-idf/spi_flash/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\spi_flash && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/spi_flash/edit_cache: phony esp-idf/spi_flash/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/spi_flash/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\spi_flash && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/spi_flash/rebuild_cache: phony esp-idf/spi_flash/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_app_format


#############################################
# Order-only phony target for __idf_esp_app_format

build cmake_object_order_depends_target___idf_esp_app_format: phony || cmake_object_order_depends_target___idf_bootloader_support

build esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj: C_COMPILER____idf_esp_app_format_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/esp_app_desc.c || cmake_object_order_depends_target___idf_esp_app_format
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE -D PROJECT_NAME=\"bootloader\" -DPROJECT_VER=\"v5.1.6\"
  DEP_FILE = esp-idf\esp_app_format\CMakeFiles\__idf_esp_app_format.dir\esp_app_desc.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include
  OBJECT_DIR = esp-idf\esp_app_format\CMakeFiles\__idf_esp_app_format.dir
  OBJECT_FILE_DIR = esp-idf\esp_app_format\CMakeFiles\__idf_esp_app_format.dir
  TARGET_COMPILE_PDB = esp-idf\esp_app_format\CMakeFiles\__idf_esp_app_format.dir\__idf_esp_app_format.pdb
  TARGET_PDB = esp-idf\esp_app_format\libesp_app_format.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_app_format


#############################################
# Link the static library esp-idf\esp_app_format\libesp_app_format.a

build esp-idf/esp_app_format/libesp_app_format.a: C_STATIC_LIBRARY_LINKER____idf_esp_app_format_ esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj || esp-idf/bootloader_support/libbootloader_support.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\esp_app_format\CMakeFiles\__idf_esp_app_format.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_app_format\CMakeFiles\__idf_esp_app_format.dir\__idf_esp_app_format.pdb
  TARGET_FILE = esp-idf\esp_app_format\libesp_app_format.a
  TARGET_PDB = esp-idf\esp_app_format\libesp_app_format.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_app_format/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_app_format && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_app_format/edit_cache: phony esp-idf/esp_app_format/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_app_format/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_app_format && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_app_format/rebuild_cache: phony esp-idf/esp_app_format/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_bootloader_support


#############################################
# Order-only phony target for __idf_bootloader_support

build cmake_object_order_depends_target___idf_bootloader_support: phony || cmake_object_order_depends_target___idf_efuse

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_common.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_common.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_common_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_common_loader.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_clock_init.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_clock_init.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_mem.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_mem.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_random.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_random.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_random_esp32s3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_random_esp32s3.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_efuse.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_efuse.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/flash_encrypt.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\flash_encrypt.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/secure_boot.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\secure_boot.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\bootloader_flash\src\bootloader_flash.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\bootloader_flash\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\bootloader_flash\src\flash_qio_mode.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\bootloader_flash\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\bootloader_flash\src\bootloader_flash_config_esp32s3.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\bootloader_flash\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_utility.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_utility.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/flash_partitions.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\flash_partitions.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/esp_image_format.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp_image_format.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_init.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_init.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_clock_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_clock_loader.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_console.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_console.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_console_loader.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_console_loader.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/esp32s3/bootloader_sha.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32s3\bootloader_sha.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32s3
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/esp32s3/bootloader_soc.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32s3\bootloader_soc.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32s3
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/esp32s3/bootloader_esp32s3.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32s3\bootloader_esp32s3.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\esp32s3
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb

build esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj: C_COMPILER____idf_bootloader_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/src/bootloader_panic.c || cmake_object_order_depends_target___idf_bootloader_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src\bootloader_panic.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  OBJECT_FILE_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\src
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_bootloader_support


#############################################
# Link the static library esp-idf\bootloader_support\libbootloader_support.a

build esp-idf/bootloader_support/libbootloader_support.a: C_STATIC_LIBRARY_LINKER____idf_bootloader_support_ esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj || esp-idf/efuse/libefuse.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\bootloader_support\CMakeFiles\__idf_bootloader_support.dir\__idf_bootloader_support.pdb
  TARGET_FILE = esp-idf\bootloader_support\libbootloader_support.a
  TARGET_PDB = esp-idf\bootloader_support\libbootloader_support.pdb


#############################################
# Utility command for edit_cache

build esp-idf/bootloader_support/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\bootloader_support && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/bootloader_support/edit_cache: phony esp-idf/bootloader_support/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/bootloader_support/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\bootloader_support && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/bootloader_support/rebuild_cache: phony esp-idf/bootloader_support/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_efuse


#############################################
# Order-only phony target for __idf_efuse

build cmake_object_order_depends_target___idf_efuse: phony || cmake_object_order_depends_target___idf_esp_system

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/esp_efuse_table.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3\esp_efuse_table.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/esp_efuse_fields.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3\esp_efuse_fields.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/esp_efuse_rtc_calib.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3\esp_efuse_rtc_calib.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/esp_efuse_utility.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3\esp_efuse_utility.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\esp32s3
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/src/esp_efuse_api.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_api.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/src/esp_efuse_fields.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_fields.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/src/esp_efuse_utility.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\esp_efuse_utility.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb

build esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj: C_COMPILER____idf_efuse_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c || cmake_object_order_depends_target___idf_efuse
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\efuse_controller\keys\with_key_purposes\esp_efuse_api_key.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  OBJECT_FILE_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\src\efuse_controller\keys\with_key_purposes
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_PDB = esp-idf\efuse\libefuse.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_efuse


#############################################
# Link the static library esp-idf\efuse\libefuse.a

build esp-idf/efuse/libefuse.a: C_STATIC_LIBRARY_LINKER____idf_efuse_ esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj || esp-idf/esp_system/libesp_system.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\efuse\CMakeFiles\__idf_efuse.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\efuse\CMakeFiles\__idf_efuse.dir\__idf_efuse.pdb
  TARGET_FILE = esp-idf\efuse\libefuse.a
  TARGET_PDB = esp-idf\efuse\libefuse.pdb


#############################################
# Utility command for efuse-common-table

build esp-idf/efuse/efuse-common-table: phony esp-idf/efuse/CMakeFiles/efuse-common-table


#############################################
# Utility command for efuse_common_table

build esp-idf/efuse/efuse_common_table: phony esp-idf/efuse/CMakeFiles/efuse_common_table esp-idf/efuse/efuse-common-table


#############################################
# Utility command for efuse-custom-table

build esp-idf/efuse/efuse-custom-table: phony


#############################################
# Utility command for efuse_custom_table

build esp-idf/efuse/efuse_custom_table: phony esp-idf/efuse/CMakeFiles/efuse_custom_table esp-idf/efuse/efuse-custom-table


#############################################
# Utility command for show-efuse-table

build esp-idf/efuse/show-efuse-table: phony esp-idf/efuse/CMakeFiles/show-efuse-table


#############################################
# Utility command for show_efuse_table

build esp-idf/efuse/show_efuse_table: phony esp-idf/efuse/CMakeFiles/show_efuse_table esp-idf/efuse/show-efuse-table


#############################################
# Utility command for efuse_test_table

build esp-idf/efuse/efuse_test_table: phony esp-idf/efuse/CMakeFiles/efuse_test_table


#############################################
# Utility command for edit_cache

build esp-idf/efuse/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/efuse/edit_cache: phony esp-idf/efuse/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/efuse/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/efuse/rebuild_cache: phony esp-idf/efuse/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse-common-table

build esp-idf/efuse/CMakeFiles/efuse-common-table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse-common-table: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/efuse_table_gen.py C:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/esp_efuse_table.csv -t esp32s3 --max_blk_len 256"


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse_common_table

build esp-idf/efuse/CMakeFiles/efuse_common_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse_common_table: CUSTOM_COMMAND || esp-idf/efuse/efuse-common-table
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E echo "
  DESC = Warning: command "efuse_common_table" is deprecated. Have you wanted to run "efuse-common-table" instead?


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse_custom_table

build esp-idf/efuse/CMakeFiles/efuse_custom_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse_custom_table: CUSTOM_COMMAND || esp-idf/efuse/efuse-custom-table
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E echo "
  DESC = Warning: command "efuse_custom_table" is deprecated. Have you wanted to run "efuse-custom-table" instead?


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\show-efuse-table

build esp-idf/efuse/CMakeFiles/show-efuse-table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/show-efuse-table: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/efuse_table_gen.py C:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/esp_efuse_table.csv -t esp32s3 --max_blk_len 256 --info"


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\show_efuse_table

build esp-idf/efuse/CMakeFiles/show_efuse_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/show_efuse_table: CUSTOM_COMMAND || esp-idf/efuse/show-efuse-table
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe -E echo "
  DESC = Warning: command "show_efuse_table" is deprecated. Have you wanted to run "show-efuse-table" instead?


#############################################
# Custom command for esp-idf\efuse\CMakeFiles\efuse_test_table

build esp-idf/efuse/CMakeFiles/efuse_test_table | ${cmake_ninja_workdir}esp-idf/efuse/CMakeFiles/efuse_test_table: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\efuse && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/efuse_table_gen.py C:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/test/esp_efuse_test_table.csv -t esp32s3 --max_blk_len 256"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_system


#############################################
# Order-only phony target for __idf_esp_system

build cmake_object_order_depends_target___idf_esp_system: phony || cmake_object_order_depends_target___idf_esp_hw_support

build esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj: C_COMPILER____idf_esp_system_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_system/esp_err.c || cmake_object_order_depends_target___idf_esp_system
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir\esp_err.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/include
  OBJECT_DIR = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir
  OBJECT_FILE_DIR = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir
  TARGET_COMPILE_PDB = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir\__idf_esp_system.pdb
  TARGET_PDB = esp-idf\esp_system\libesp_system.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_system


#############################################
# Link the static library esp-idf\esp_system\libesp_system.a

build esp-idf/esp_system/libesp_system.a: C_STATIC_LIBRARY_LINKER____idf_esp_system_ esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj || esp-idf/esp_hw_support/libesp_hw_support.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_system\CMakeFiles\__idf_esp_system.dir\__idf_esp_system.pdb
  TARGET_FILE = esp-idf\esp_system\libesp_system.a
  TARGET_PDB = esp-idf\esp_system\libesp_system.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_system/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_system && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_system/edit_cache: phony esp-idf/esp_system/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_system/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_system && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_system/rebuild_cache: phony esp-idf/esp_system/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_hw_support


#############################################
# Order-only phony target for __idf_esp_hw_support

build cmake_object_order_depends_target___idf_esp_hw_support: phony || cmake_object_order_depends_target___idf_esp_common

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/cpu.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\cpu.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/esp_cpu_intr.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\esp_cpu_intr.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/esp_memory_utils.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\esp_memory_utils.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/cpu_region_protect.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\cpu_region_protect.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\rtc_clk.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/rtc_clk_init.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\rtc_clk_init.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/rtc_init.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\rtc_init.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/rtc_sleep.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\rtc_sleep.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/rtc_time.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\rtc_time.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb

build esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj: C_COMPILER____idf_esp_hw_support_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/chip_info.c || cmake_object_order_depends_target___idf_esp_hw_support
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3\chip_info.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration -Wno-format
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/esp_private -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  OBJECT_FILE_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\port\esp32s3
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_hw_support


#############################################
# Link the static library esp-idf\esp_hw_support\libesp_hw_support.a

build esp-idf/esp_hw_support/libesp_hw_support.a: C_STATIC_LIBRARY_LINKER____idf_esp_hw_support_ esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj || esp-idf/esp_common/libesp_common.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_hw_support\CMakeFiles\__idf_esp_hw_support.dir\__idf_esp_hw_support.pdb
  TARGET_FILE = esp-idf\esp_hw_support\libesp_hw_support.a
  TARGET_PDB = esp-idf\esp_hw_support\libesp_hw_support.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_hw_support/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_hw_support && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/edit_cache: phony esp-idf/esp_hw_support/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_hw_support/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_hw_support && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/rebuild_cache: phony esp-idf/esp_hw_support/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/esp_hw_support/port/esp32s3/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_hw_support\port\esp32s3 && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/port/esp32s3/edit_cache: phony esp-idf/esp_hw_support/port/esp32s3/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_hw_support/port/esp32s3/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_hw_support\port\esp32s3 && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_hw_support/port/esp32s3/rebuild_cache: phony esp-idf/esp_hw_support/port/esp32s3/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_common


#############################################
# Order-only phony target for __idf_esp_common

build cmake_object_order_depends_target___idf_esp_common: phony || cmake_object_order_depends_target___idf_esp_rom

build esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj: C_COMPILER____idf_esp_common_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/src/esp_err_to_name.c || cmake_object_order_depends_target___idf_esp_common
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\src\esp_err_to_name.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir
  OBJECT_FILE_DIR = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\src
  TARGET_COMPILE_PDB = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\__idf_esp_common.pdb
  TARGET_PDB = esp-idf\esp_common\libesp_common.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_common


#############################################
# Link the static library esp-idf\esp_common\libesp_common.a

build esp-idf/esp_common/libesp_common.a: C_STATIC_LIBRARY_LINKER____idf_esp_common_ esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj || esp-idf/esp_rom/libesp_rom.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_common\CMakeFiles\__idf_esp_common.dir\__idf_esp_common.pdb
  TARGET_FILE = esp-idf\esp_common\libesp_common.a
  TARGET_PDB = esp-idf\esp_common\libesp_common.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_common/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_common && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_common/edit_cache: phony esp-idf/esp_common/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_common/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_common && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_common/rebuild_cache: phony esp-idf/esp_common/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_esp_rom


#############################################
# Order-only phony target for __idf_esp_rom

build cmake_object_order_depends_target___idf_esp_rom: phony || cmake_object_order_depends_target___idf_log

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_crc.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_crc.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_sys.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_sys.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_uart.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_uart.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_spiflash.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_efuse.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_efuse.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_gpio.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_gpio.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj: ASM_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_longjmp.S.obj.d
  FLAGS = -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_systimer.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_systimer.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_wdt.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_wdt.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj: C_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_cache_esp32s2_esp32s3.c || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_cache_esp32s2_esp32s3.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb

build esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj: ASM_COMPILER____idf_esp_rom_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/patches/esp_rom_cache_writeback_esp32s3.S || cmake_object_order_depends_target___idf_esp_rom
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches\esp_rom_cache_writeback_esp32s3.S.obj.d
  FLAGS = -mlongcalls  -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  OBJECT_FILE_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\patches
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_esp_rom


#############################################
# Link the static library esp-idf\esp_rom\libesp_rom.a

build esp-idf/esp_rom/libesp_rom.a: C_STATIC_LIBRARY_LINKER____idf_esp_rom_ esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj || esp-idf/log/liblog.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\esp_rom\CMakeFiles\__idf_esp_rom.dir\__idf_esp_rom.pdb
  TARGET_FILE = esp-idf\esp_rom\libesp_rom.a
  TARGET_PDB = esp-idf\esp_rom\libesp_rom.pdb


#############################################
# Utility command for edit_cache

build esp-idf/esp_rom/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_rom && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esp_rom/edit_cache: phony esp-idf/esp_rom/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esp_rom/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esp_rom && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esp_rom/rebuild_cache: phony esp-idf/esp_rom/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_log


#############################################
# Order-only phony target for __idf_log

build cmake_object_order_depends_target___idf_log: phony || .

build esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj: C_COMPILER____idf_log_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/log.c || cmake_object_order_depends_target___idf_log
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\log\CMakeFiles\__idf_log.dir\log.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  OBJECT_FILE_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_PDB = esp-idf\log\liblog.pdb

build esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj: C_COMPILER____idf_log_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/log_buffers.c || cmake_object_order_depends_target___idf_log
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\log\CMakeFiles\__idf_log.dir\log_buffers.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  OBJECT_FILE_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_PDB = esp-idf\log\liblog.pdb

build esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj: C_COMPILER____idf_log_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/log_noos.c || cmake_object_order_depends_target___idf_log
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\log\CMakeFiles\__idf_log.dir\log_noos.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/platform_port/include
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  OBJECT_FILE_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_PDB = esp-idf\log\liblog.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_log


#############################################
# Link the static library esp-idf\log\liblog.a

build esp-idf/log/liblog.a: C_STATIC_LIBRARY_LINKER____idf_log_ esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\log\CMakeFiles\__idf_log.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\log\CMakeFiles\__idf_log.dir\__idf_log.pdb
  TARGET_FILE = esp-idf\log\liblog.a
  TARGET_PDB = esp-idf\log\liblog.pdb


#############################################
# Utility command for edit_cache

build esp-idf/log/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\log && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/log/edit_cache: phony esp-idf/log/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/log/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\log && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/log/rebuild_cache: phony esp-idf/log/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for bootloader_check_size

build esp-idf/esptool_py/bootloader_check_size: phony esp-idf/esptool_py/CMakeFiles/bootloader_check_size gen_project_binary


#############################################
# Utility command for edit_cache

build esp-idf/esptool_py/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/esptool_py/edit_cache: phony esp-idf/esptool_py/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/esptool_py/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/esptool_py/rebuild_cache: phony esp-idf/esptool_py/CMakeFiles/rebuild_cache.util


#############################################
# Custom command for esp-idf\esptool_py\CMakeFiles\bootloader_check_size

build esp-idf/esptool_py/CMakeFiles/bootloader_check_size | ${cmake_ninja_workdir}esp-idf/esptool_py/CMakeFiles/bootloader_check_size: CUSTOM_COMMAND || _project_elf_src bootloader.elf esp-idf/bootloader_support/libbootloader_support.a esp-idf/efuse/libefuse.a esp-idf/esp_app_format/libesp_app_format.a esp-idf/esp_common/libesp_common.a esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_rom/libesp_rom.a esp-idf/esp_system/libesp_system.a esp-idf/hal/libhal.a esp-idf/log/liblog.a esp-idf/main/libmain.a esp-idf/micro-ecc/libmicro-ecc.a esp-idf/soc/libsoc.a esp-idf/spi_flash/libspi_flash.a esp-idf/xtensa/libxtensa.a gen_project_binary
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\esptool_py && c:\Users\<USER>\EspressIF\tools\python_env\idf5.1_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.1.6/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/bootloader.bin"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/partition_table/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\partition_table && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/partition_table/edit_cache: phony esp-idf/partition_table/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/partition_table/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\partition_table && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/partition_table/rebuild_cache: phony esp-idf/partition_table/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/bootloader/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/bootloader/edit_cache: phony esp-idf/bootloader/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/bootloader/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\bootloader && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/bootloader/rebuild_cache: phony esp-idf/bootloader/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build esp-idf/freertos/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\freertos && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/freertos/edit_cache: phony esp-idf/freertos/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/freertos/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\freertos && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/freertos/rebuild_cache: phony esp-idf/freertos/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target __idf_main


#############################################
# Order-only phony target for __idf_main

build cmake_object_order_depends_target___idf_main: phony || cmake_object_order_depends_target___idf_xtensa

build esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj: C_COMPILER____idf_main_unscanned_ C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/main/bootloader_start.c || cmake_object_order_depends_target___idf_main
  DEFINES = -DBOOTLOADER_BUILD=1 -DESP_PLATFORM -DIDF_VER=\"v5.1.6\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -D_GNU_SOURCE
  DEP_FILE = esp-idf\main\CMakeFiles\__idf_main.dir\bootloader_start.c.obj.d
  FLAGS = -mlongcalls  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -Os -freorder-blocks -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject=. -fmacro-prefix-map=C:/Users/<USER>/esp/v5.1.6/esp-idf=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -fno-stack-protector -std=gnu17 -Wno-old-style-declaration
  INCLUDES = -IC:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/config -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/include/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/include/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/. -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/platform_include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3 -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/esp32s3/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/bootloader_flash/include -IC:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/private_include
  OBJECT_DIR = esp-idf\main\CMakeFiles\__idf_main.dir
  OBJECT_FILE_DIR = esp-idf\main\CMakeFiles\__idf_main.dir
  TARGET_COMPILE_PDB = esp-idf\main\CMakeFiles\__idf_main.dir\__idf_main.pdb
  TARGET_PDB = esp-idf\main\libmain.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target __idf_main


#############################################
# Link the static library esp-idf\main\libmain.a

build esp-idf/main/libmain.a: C_STATIC_LIBRARY_LINKER____idf_main_ esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj || esp-idf/xtensa/libxtensa.a
  LANGUAGE_COMPILE_FLAGS = -mlongcalls
  OBJECT_DIR = esp-idf\main\CMakeFiles\__idf_main.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = esp-idf\main\CMakeFiles\__idf_main.dir\__idf_main.pdb
  TARGET_FILE = esp-idf\main\libmain.a
  TARGET_PDB = esp-idf\main\libmain.pdb


#############################################
# Utility command for edit_cache

build esp-idf/main/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\main && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake-gui.exe -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build esp-idf/main/edit_cache: phony esp-idf/main/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build esp-idf/main/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader\esp-idf\main && C:\Users\<USER>\EspressIF\tools\tools\cmake\3.30.2\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\esp\v5.1.6\esp-idf\components\bootloader\subproject -BC:\Users\<USER>\vscode\projects-lvgl\cdc_acm_host\build\bootloader"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build esp-idf/main/rebuild_cache: phony esp-idf/main/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build __idf_bootloader_support: phony esp-idf/bootloader_support/libbootloader_support.a

build __idf_efuse: phony esp-idf/efuse/libefuse.a

build __idf_esp_app_format: phony esp-idf/esp_app_format/libesp_app_format.a

build __idf_esp_common: phony esp-idf/esp_common/libesp_common.a

build __idf_esp_hw_support: phony esp-idf/esp_hw_support/libesp_hw_support.a

build __idf_esp_rom: phony esp-idf/esp_rom/libesp_rom.a

build __idf_esp_system: phony esp-idf/esp_system/libesp_system.a

build __idf_hal: phony esp-idf/hal/libhal.a

build __idf_log: phony esp-idf/log/liblog.a

build __idf_main: phony esp-idf/main/libmain.a

build __idf_micro-ecc: phony esp-idf/micro-ecc/libmicro-ecc.a

build __idf_soc: phony esp-idf/soc/libsoc.a

build __idf_spi_flash: phony esp-idf/spi_flash/libspi_flash.a

build __idf_xtensa: phony esp-idf/xtensa/libxtensa.a

build bootloader_check_size: phony esp-idf/esptool_py/bootloader_check_size

build efuse-common-table: phony esp-idf/efuse/efuse-common-table

build efuse-custom-table: phony esp-idf/efuse/efuse-custom-table

build efuse_common_table: phony esp-idf/efuse/efuse_common_table

build efuse_custom_table: phony esp-idf/efuse/efuse_custom_table

build efuse_test_table: phony esp-idf/efuse/efuse_test_table

build libbootloader_support.a: phony esp-idf/bootloader_support/libbootloader_support.a

build libefuse.a: phony esp-idf/efuse/libefuse.a

build libesp_app_format.a: phony esp-idf/esp_app_format/libesp_app_format.a

build libesp_common.a: phony esp-idf/esp_common/libesp_common.a

build libesp_hw_support.a: phony esp-idf/esp_hw_support/libesp_hw_support.a

build libesp_rom.a: phony esp-idf/esp_rom/libesp_rom.a

build libesp_system.a: phony esp-idf/esp_system/libesp_system.a

build libhal.a: phony esp-idf/hal/libhal.a

build liblog.a: phony esp-idf/log/liblog.a

build libmain.a: phony esp-idf/main/libmain.a

build libmicro-ecc.a: phony esp-idf/micro-ecc/libmicro-ecc.a

build libsoc.a: phony esp-idf/soc/libsoc.a

build libspi_flash.a: phony esp-idf/spi_flash/libspi_flash.a

build libxtensa.a: phony esp-idf/xtensa/libxtensa.a

build show-efuse-table: phony esp-idf/efuse/show-efuse-table

build show_efuse_table: phony esp-idf/efuse/show_efuse_table

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader

build all: phony app bootloader.elf esp-idf/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf

build esp-idf/all: phony esp-idf/xtensa/all esp-idf/newlib/all esp-idf/soc/all esp-idf/micro-ecc/all esp-idf/hal/all esp-idf/spi_flash/all esp-idf/esp_app_format/all esp-idf/bootloader_support/all esp-idf/efuse/all esp-idf/esp_system/all esp-idf/esp_hw_support/all esp-idf/esp_common/all esp-idf/esp_rom/all esp-idf/log/all esp-idf/esptool_py/all esp-idf/partition_table/all esp-idf/bootloader/all esp-idf/freertos/all esp-idf/main/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/bootloader

build esp-idf/bootloader/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/bootloader_support

build esp-idf/bootloader_support/all: phony esp-idf/bootloader_support/libbootloader_support.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/efuse

build esp-idf/efuse/all: phony esp-idf/efuse/libefuse.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esp_app_format

build esp-idf/esp_app_format/all: phony esp-idf/esp_app_format/libesp_app_format.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esp_common

build esp-idf/esp_common/all: phony esp-idf/esp_common/libesp_common.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esp_hw_support

build esp-idf/esp_hw_support/all: phony esp-idf/esp_hw_support/libesp_hw_support.a esp-idf/esp_hw_support/port/esp32s3/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esp_hw_support/port/esp32s3

build esp-idf/esp_hw_support/port/esp32s3/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esp_rom

build esp-idf/esp_rom/all: phony esp-idf/esp_rom/libesp_rom.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esp_system

build esp-idf/esp_system/all: phony esp-idf/esp_system/libesp_system.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esptool_py

build esp-idf/esptool_py/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/freertos

build esp-idf/freertos/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/hal

build esp-idf/hal/all: phony esp-idf/hal/libhal.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/log

build esp-idf/log/all: phony esp-idf/log/liblog.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/main

build esp-idf/main/all: phony esp-idf/main/libmain.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/micro-ecc

build esp-idf/micro-ecc/all: phony esp-idf/micro-ecc/libmicro-ecc.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/newlib

build esp-idf/newlib/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/partition_table

build esp-idf/partition_table/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/soc

build esp-idf/soc/all: phony esp-idf/soc/libsoc.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/spi_flash

build esp-idf/spi_flash/all: phony esp-idf/spi_flash/libspi_flash.a

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/xtensa

build esp-idf/xtensa/all: phony esp-idf/xtensa/libxtensa.a

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeASMCompiler.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeASMInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineASMCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestASMCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCCompilerFlag.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCXXCompilerFlag.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckIncludeFile.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckIncludeFileCXX.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckTypeSize.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-ASM.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/ExternalProject.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/ExternalProject/shared_internal_commands.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/FindGit.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/FindPackageMessage.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CheckCompilerFlag.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CheckFlagCommonConfig.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Generic.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/host/nimble/nimble/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/cmock/CMock/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/esp_coex/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/esp_phy/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/esp_wifi/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/heap/tlsf/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/json/cJSON/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/lwip/lwip/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/mbedtls/mbedtls/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/mqtt/esp-mqtt/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/openthread/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/openthread/openthread/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/protobuf-c/protobuf-c/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/spiffs/spiffs/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/unity/unity/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/main/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32c3_family/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/host/nimble/nimble/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/cmock/CMock/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/sources.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_coex/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_phy/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_system/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_wifi/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/espefuse.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/freertos/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/heap/tlsf/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/json/cJSON/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/lwip/lwip/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/mbedtls/mbedtls/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/mqtt/esp-mqtt/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/openthread/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/openthread/openthread/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/partition_table/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/partition_table/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/protobuf-c/protobuf-c/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/spiffs/spiffs/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/unity/unity/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/build.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/component.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/depgraph.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/dfu.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/git_submodules.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/idf.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/kconfig.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/ldgen.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/project.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/project_description.json.in C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/targets.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/third_party/GetGitRevisionDescription.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/third_party/GetGitRevisionDescription.cmake.in C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/tool_version_check.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/toolchain-esp32s3.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/uf2.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/utilities.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/version.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/confgen.py C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/config.env.in C$:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig CMakeCache.txt CMakeFiles/3.30.2/CMakeASMCompiler.cmake CMakeFiles/3.30.2/CMakeCCompiler.cmake CMakeFiles/3.30.2/CMakeCXXCompiler.cmake CMakeFiles/3.30.2/CMakeSystem.cmake CMakeFiles/git-data/grabRef.cmake config/sdkconfig.cmake config/sdkconfig.h
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeASMCompiler.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeASMInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineASMCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineCompilerSupport.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeNinjaFindMake.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestASMCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCCompilerFlag.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCXXCompilerFlag.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckCXXSourceCompiles.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckIncludeFile.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckIncludeFileCXX.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/CheckTypeSize.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/CrayClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-ASM.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-C.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-CXX.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU-FindBinUtils.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/GNU.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/OrangeC-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TIClang-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Tasking-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/ExternalProject.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/ExternalProject/shared_internal_commands.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/FindGit.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/FindPackageHandleStandardArgs.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/FindPackageMessage.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CheckCompilerFlag.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CheckFlagCommonConfig.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/EspressIF/tools/tools/cmake/3.30.2/share/cmake-3.30/Modules/Platform/Generic.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bootloader/subproject/components/micro-ecc/micro-ecc/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32c3_family/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/bt/host/nimble/nimble/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/cmock/CMock/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/esp_coex/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/esp_phy/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/esp_wifi/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/heap/tlsf/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/json/cJSON/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/lwip/lwip/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/mbedtls/mbedtls/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/mqtt/esp-mqtt/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/openthread/lib/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/openthread/openthread/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/protobuf-c/protobuf-c/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/spiffs/spiffs/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/.git/modules/components/unity/unity/HEAD C$:/Users/<USER>/esp/v5.1.6/esp-idf/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/components/micro-ecc/micro-ecc/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader/subproject/main/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bootloader_support/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32c2/esp32c2-bt-lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32c3_family/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32c6/esp32c6-bt-lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/controller/lib_esp32h2/esp32h2-bt-lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/bt/host/nimble/nimble/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/cmock/CMock/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/efuse/esp32s3/sources.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_app_format/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_coex/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_common/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_hw_support/port/esp32s3/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_phy/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_rom/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_system/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esp_wifi/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/espefuse.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/esptool_py/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/freertos/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/hal/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/heap/tlsf/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/json/cJSON/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/log/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/lwip/lwip/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/mbedtls/mbedtls/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/mqtt/esp-mqtt/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/newlib/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/openthread/lib/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/openthread/openthread/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/partition_table/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/partition_table/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/protobuf-c/protobuf-c/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/soc/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/spi_flash/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/spiffs/spiffs/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/unity/unity/.git C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/CMakeLists.txt C$:/Users/<USER>/esp/v5.1.6/esp-idf/components/xtensa/project_include.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/build.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/component.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/depgraph.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/dfu.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/git_submodules.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/idf.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/kconfig.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/ldgen.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/project.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/project_description.json.in C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/targets.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/third_party/GetGitRevisionDescription.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/third_party/GetGitRevisionDescription.cmake.in C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/tool_version_check.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/toolchain-esp32s3.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/uf2.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/utilities.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/cmake/version.cmake C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/confgen.py C$:/Users/<USER>/esp/v5.1.6/esp-idf/tools/kconfig_new/config.env.in C$:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/sdkconfig CMakeCache.txt CMakeFiles/3.30.2/CMakeASMCompiler.cmake CMakeFiles/3.30.2/CMakeCCompiler.cmake CMakeFiles/3.30.2/CMakeCXXCompiler.cmake CMakeFiles/3.30.2/CMakeSystem.cmake CMakeFiles/git-data/grabRef.cmake config/sdkconfig.cmake config/sdkconfig.h: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
