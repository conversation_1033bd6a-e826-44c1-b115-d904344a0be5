/*
 * SPDX-FileCopyrightText: 2015-2023 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: CC0-1.0
 */

#include <stdio.h>
#include <string.h>
#include <inttypes.h>
#include "esp_system.h"
#include "esp_log.h"
#include "esp_err.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

#include "usb/usb_host.h"
#include "usb/cdc_acm_host.h"

#define EXAMPLE_USB_HOST_PRIORITY   (20)
#define HLK_TX510_BAUDRATE         (115200)
#define HLK_TX510_TIMEOUT_MS       (1000)

// HLK-TX510 Protocol Constants
#define HLK_SYNC_WORD_1            (0xEF)
#define HLK_SYNC_WORD_2            (0xAA)
#define HLK_CMD_DISPLAY_OFF        (0xC1)
#define HLK_CMD_DISPLAY_ON         (0xC1)
#define HLK_CMD_START_RECOGNITION  (0x12)
#define HLK_CMD_REGISTER_FACE      (0x13)

static const char *TAG = "HLK-TX510";
static SemaphoreHandle_t device_disconnected_sem;
static cdc_acm_dev_hdl_t hlk_device = NULL;

// HLK-TX510 Command Structure
typedef struct {
    uint8_t sync_word[2];  // EF AA
    uint8_t msg_id;        // Command ID
    uint8_t size[4];       // Data size (little endian)
    uint8_t data[];        // Variable length data
    // Checksum is calculated and added after data
} __attribute__((packed)) hlk_command_t;

/**
 * @brief Calculate HLK-TX510 checksum
 *
 * @param data Pointer to data after sync word
 * @param len Length of data
 * @return Calculated checksum
 */
static uint8_t hlk_calculate_checksum(const uint8_t *data, size_t len)
{
    uint8_t checksum = 0;
    for (size_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief Build HLK-TX510 command
 *
 * @param cmd_id Command ID
 * @param data Command data (can be NULL)
 * @param data_len Length of command data
 * @param buffer Output buffer for complete command
 * @param buffer_size Size of output buffer
 * @return Length of built command, or 0 on error
 */
static size_t hlk_build_command(uint8_t cmd_id, const uint8_t *data, uint32_t data_len,
                               uint8_t *buffer, size_t buffer_size)
{
    size_t total_len = 7 + data_len + 1; // sync(2) + cmd(1) + size(4) + data + checksum(1)

    if (buffer_size < total_len) {
        ESP_LOGE(TAG, "Buffer too small for command");
        return 0;
    }

    // Build command
    buffer[0] = HLK_SYNC_WORD_1;
    buffer[1] = HLK_SYNC_WORD_2;
    buffer[2] = cmd_id;

    // Size in little endian format
    buffer[3] = data_len & 0xFF;
    buffer[4] = (data_len >> 8) & 0xFF;
    buffer[5] = (data_len >> 16) & 0xFF;
    buffer[6] = (data_len >> 24) & 0xFF;

    // Copy data if present
    if (data && data_len > 0) {
        memcpy(&buffer[7], data, data_len);
    }

    // Calculate and add checksum (everything after sync word)
    uint8_t checksum = hlk_calculate_checksum(&buffer[2], 5 + data_len);
    buffer[7 + data_len] = checksum;

    return total_len;
}

/**
 * @brief Data received callback for HLK-TX510 responses
 *
 * @param[in] data     Pointer to received data
 * @param[in] data_len Length of received data in bytes
 * @param[in] arg      Argument we passed to the device open function
 * @return
 *   true:  We have processed the received data
 *   false: We expect more data
 */
static bool handle_hlk_rx(const uint8_t *data, size_t data_len, void *arg)
{
    ESP_LOGI(TAG, "HLK-TX510 Response received (%d bytes):", data_len);
    ESP_LOG_BUFFER_HEXDUMP(TAG, data, data_len, ESP_LOG_INFO);

    // Parse HLK-TX510 response
    if (data_len >= 2 && data[0] == HLK_SYNC_WORD_1 && data[1] == HLK_SYNC_WORD_2) {
        if (data_len >= 7) {
            uint8_t msg_id = data[2];
            uint32_t size = data[3] | (data[4] << 8) | (data[5] << 16) | (data[6] << 24);

            ESP_LOGI(TAG, "HLK Response - MsgID: 0x%02X, Size: %lu", msg_id, size);

            switch (msg_id) {
                case 0x00: // Face recognition response
                    if (size >= 4 && data_len >= 11) {
                        uint8_t cmd_type = data[7];
                        uint16_t face_id = data[8] | (data[9] << 8);

                        if (cmd_type == 0x12) {
                            // Face recognition result
                            if (face_id == 0xFFFF) {
                                ESP_LOGW(TAG, "Face Recognition: No face detected or unknown face");
                            } else {
                                ESP_LOGI(TAG, "Face Recognition: SUCCESS - Face ID: %d", face_id);
                            }
                        } else if (cmd_type == 0x13) {
                            // Face registration result
                            if (face_id == 0xFFFF) {
                                ESP_LOGW(TAG, "Face Registration: FAILED");
                            } else {
                                ESP_LOGI(TAG, "Face Registration: SUCCESS - Assigned Face ID: %d", face_id);
                            }
                        }
                    }
                    break;

                case 0x12: // Recognition command response
                    ESP_LOGI(TAG, "Face recognition command acknowledged");
                    break;

                case 0x13: // Registration command response
                    ESP_LOGI(TAG, "Face registration command acknowledged");
                    break;

                case 0xC1: // Display command response
                    ESP_LOGI(TAG, "Display command acknowledged");
                    break;

                default:
                    ESP_LOGI(TAG, "Unknown command response: 0x%02X", msg_id);
                    break;
            }
        }
    } else {
        ESP_LOGW(TAG, "Invalid HLK response format - Expected EF AA sync word");
    }

    return true;
}

/**
 * @brief Send HLK-TX510 command
 *
 * @param cmd_id Command ID
 * @param data Command data (can be NULL)
 * @param data_len Length of command data
 * @return ESP_OK on success
 */
static esp_err_t hlk_send_command(uint8_t cmd_id, const uint8_t *data, uint32_t data_len)
{
    if (!hlk_device) {
        ESP_LOGE(TAG, "HLK device not connected");
        return ESP_ERR_INVALID_STATE;
    }

    uint8_t command_buffer[64];
    size_t cmd_len = hlk_build_command(cmd_id, data, data_len, command_buffer, sizeof(command_buffer));

    if (cmd_len == 0) {
        ESP_LOGE(TAG, "Failed to build HLK command");
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Sending HLK command 0x%02X (%d bytes):", cmd_id, cmd_len);
    ESP_LOG_BUFFER_HEXDUMP(TAG, command_buffer, cmd_len, ESP_LOG_INFO);

    esp_err_t ret = cdc_acm_host_data_tx_blocking(hlk_device, command_buffer, cmd_len, HLK_TX510_TIMEOUT_MS);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to send command 0x%02X: %s", cmd_id, esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "Command 0x%02X sent successfully", cmd_id);
    }

    return ret;
}

/**
 * @brief Device event callback
 *
 * Apart from handling device disconnection it doesn't do anything useful
 *
 * @param[in] event    Device event type and data
 * @param[in] user_ctx Argument we passed to the device open function
 */
static void handle_event(const cdc_acm_host_dev_event_data_t *event, void *user_ctx)
{
    switch (event->type) {
        case CDC_ACM_HOST_ERROR:
            ESP_LOGE(TAG, "CDC-ACM error has occurred, err_no = %i", event->data.error);
            break;
        case CDC_ACM_HOST_DEVICE_DISCONNECTED:
            ESP_LOGI(TAG, "HLK-TX510 device suddenly disconnected");
            ESP_ERROR_CHECK(cdc_acm_host_close(event->data.cdc_hdl));
            hlk_device = NULL;
            xSemaphoreGive(device_disconnected_sem);
            break;
        case CDC_ACM_HOST_SERIAL_STATE:
            ESP_LOGI(TAG, "Serial state notif 0x%04X", event->data.serial_state.val);
            break;
        case CDC_ACM_HOST_NETWORK_CONNECTION:
        default:
            ESP_LOGW(TAG, "Unsupported CDC event: %i", event->type);
            break;
    }
}

/**
 * @brief USB Host library handling task
 *
 * @param arg Unused
 */
static void usb_lib_task(void *arg)
{
    while (1) {
        // Start handling system events
        uint32_t event_flags;
        usb_host_lib_handle_events(portMAX_DELAY, &event_flags);
        if (event_flags & USB_HOST_LIB_EVENT_FLAGS_NO_CLIENTS) {
            ESP_ERROR_CHECK(usb_host_device_free_all());
        }
        if (event_flags & USB_HOST_LIB_EVENT_FLAGS_ALL_FREE) {
            ESP_LOGI(TAG, "USB: All devices freed");
            // Continue handling USB events to allow device reconnection
        }
    }
}

/**
 * @brief Test HLK-TX510 display commands
 */
static void test_hlk_display_commands(void)
{
    ESP_LOGI(TAG, "Testing HLK-TX510 display commands...");

    // Test display off command
    ESP_LOGI(TAG, "Sending display OFF command");
    uint8_t display_off_data = 0x00;
    esp_err_t ret = hlk_send_command(HLK_CMD_DISPLAY_OFF, &display_off_data, 1);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Display OFF command sent successfully");
    } else {
        ESP_LOGE(TAG, "Failed to send display OFF command: %s", esp_err_to_name(ret));
    }

    vTaskDelay(pdMS_TO_TICKS(2000)); // Wait 2 seconds

    // Test display on command
    ESP_LOGI(TAG, "Sending display ON command");
    uint8_t display_on_data = 0x01;
    ret = hlk_send_command(HLK_CMD_DISPLAY_ON, &display_on_data, 1);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Display ON command sent successfully");
    } else {
        ESP_LOGE(TAG, "Failed to send display ON command: %s", esp_err_to_name(ret));
    }
}

/**
 * @brief Test HLK-TX510 face recognition commands
 */
static void test_hlk_face_recognition(void)
{
    ESP_LOGI(TAG, "Testing HLK-TX510 face recognition commands...");

    // Test face registration command
    ESP_LOGI(TAG, "Sending face REGISTRATION command");
    esp_err_t ret = hlk_send_command(HLK_CMD_REGISTER_FACE, NULL, 0);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Face registration command sent successfully");
        ESP_LOGI(TAG, "Please position your face in front of the camera for registration...");
    } else {
        ESP_LOGE(TAG, "Failed to send face registration command: %s", esp_err_to_name(ret));
    }

    vTaskDelay(pdMS_TO_TICKS(5000)); // Wait 5 seconds for registration

    // Test face recognition command
    ESP_LOGI(TAG, "Sending face RECOGNITION command");
    ret = hlk_send_command(HLK_CMD_START_RECOGNITION, NULL, 0);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Face recognition command sent successfully");
        ESP_LOGI(TAG, "Please position your face in front of the camera for recognition...");
    } else {
        ESP_LOGE(TAG, "Failed to send face recognition command: %s", esp_err_to_name(ret));
    }

    vTaskDelay(pdMS_TO_TICKS(3000)); // Wait 3 seconds for recognition
}

/**
 * @brief Send individual HLK-TX510 commands for testing
 */
static void send_hlk_test_commands(void)
{
    ESP_LOGI(TAG, "Sending individual HLK-TX510 test commands...");

    // Send display off
    ESP_LOGI(TAG, "1. Display OFF");
    uint8_t display_off = 0x00;
    hlk_send_command(HLK_CMD_DISPLAY_OFF, &display_off, 1);
    vTaskDelay(pdMS_TO_TICKS(1000));

    // Send display on
    ESP_LOGI(TAG, "2. Display ON");
    uint8_t display_on = 0x01;
    hlk_send_command(HLK_CMD_DISPLAY_ON, &display_on, 1);
    vTaskDelay(pdMS_TO_TICKS(1000));

    // Send face registration
    ESP_LOGI(TAG, "3. Face Registration");
    hlk_send_command(HLK_CMD_REGISTER_FACE, NULL, 0);
    vTaskDelay(pdMS_TO_TICKS(2000));

    // Send face recognition
    ESP_LOGI(TAG, "4. Face Recognition");
    hlk_send_command(HLK_CMD_START_RECOGNITION, NULL, 0);
    vTaskDelay(pdMS_TO_TICKS(2000));
}

/**
 * @brief Complete HLK-TX510 test sequence
 */
static void test_hlk_complete_sequence(void)
{
    ESP_LOGI(TAG, "Starting complete HLK-TX510 test sequence...");

    // Step 1: Test display commands
    test_hlk_display_commands();
    vTaskDelay(pdMS_TO_TICKS(1000));

    // Step 2: Test face recognition
    test_hlk_face_recognition();
    vTaskDelay(pdMS_TO_TICKS(1000));

    ESP_LOGI(TAG, "Complete HLK-TX510 test sequence finished!");
}

/**
 * @brief Print user menu
 */
static void print_user_menu(void)
{
    ESP_LOGI(TAG, "\n=== HLK-TX510 Control Menu ===");
    ESP_LOGI(TAG, "Commands available via serial monitor:");
    ESP_LOGI(TAG, "  1 - Display OFF");
    ESP_LOGI(TAG, "  2 - Display ON");
    ESP_LOGI(TAG, "  3 - Register Face");
    ESP_LOGI(TAG, "  4 - Start Recognition");
    ESP_LOGI(TAG, "  5 - Run Complete Test");
    ESP_LOGI(TAG, "  6 - Send Test Commands");
    ESP_LOGI(TAG, "  h - Show this menu");
    ESP_LOGI(TAG, "==============================\n");
}

/**
 * @brief Interactive test task
 */
static void interactive_test_task(void *arg)
{
    print_user_menu();

    while (true) {
        // In a real implementation, you would read from UART here
        // For now, we'll just run the complete test sequence periodically
        ESP_LOGI(TAG, "Running automatic test cycle...");
        send_hlk_test_commands();

        ESP_LOGI(TAG, "Waiting 30 seconds before next test cycle...");
        vTaskDelay(pdMS_TO_TICKS(30000)); // Wait 30 seconds
    }
}

/**
 * @brief Main application
 *
 * Here we open a USB CDC device and communicate with HLK-TX510
 */
void app_main(void)
{
    device_disconnected_sem = xSemaphoreCreateBinary();
    assert(device_disconnected_sem);

    // Install USB Host driver. Should only be called once in entire application
    ESP_LOGI(TAG, "Installing USB Host");
    const usb_host_config_t host_config = {
        .skip_phy_setup = false,
        .intr_flags = ESP_INTR_FLAG_LEVEL1,
    };
    ESP_ERROR_CHECK(usb_host_install(&host_config));

    // Create a task that will handle USB library events
    BaseType_t task_created = xTaskCreate(usb_lib_task, "usb_lib", 4096, xTaskGetCurrentTaskHandle(), EXAMPLE_USB_HOST_PRIORITY, NULL);
    assert(task_created == pdTRUE);

    ESP_LOGI(TAG, "Installing CDC-ACM driver");
    ESP_ERROR_CHECK(cdc_acm_host_install(NULL));

    const cdc_acm_host_device_config_t dev_config = {
        .connection_timeout_ms = 5000,  // Increased timeout for HLK-TX510
        .out_buffer_size = 512,
        .in_buffer_size = 512,
        .user_arg = NULL,
        .event_cb = handle_event,
        .data_cb = handle_hlk_rx
    };

    ESP_LOGI(TAG, "Waiting for HLK-TX510 device connection...");

    while (true) {
        // Try to open any CDC ACM device (we'll identify HLK-TX510 by communication)
        ESP_LOGI(TAG, "Scanning for CDC ACM devices...");

        // We'll try common VID/PID combinations or use a more generic approach
        // For now, let's try to open any CDC device by trying different VID/PID combinations
        esp_err_t err = ESP_ERR_NOT_FOUND;

        // Common CDC device VID/PID combinations to try
        uint16_t vid_pid_list[][2] = {
            {0x1A86, 0x7523}, // CH340 series (very common)
            {0x1A86, 0x55D4}, // CH340 variant
            {0x10C4, 0xEA60}, // CP210x series
            {0x0403, 0x6001}, // FTDI FT232
            {0x0403, 0x6015}, // FTDI FT231X
            {0x067B, 0x2303}, // PL2303
            {0x067B, 0x23A3}, // PL2303 variant
            {0x2341, 0x0043}, // Arduino Uno
            {0x2341, 0x0001}, // Arduino Uno variant
            {0x303A, 0x4001}, // ESP32 CDC (for testing)
            {0x303A, 0x4002}, // ESP32 Dual CDC (for testing)
        };

        for (int i = 0; i < sizeof(vid_pid_list) / sizeof(vid_pid_list[0]); i++) {
            ESP_LOGI(TAG, "Trying to open CDC device 0x%04X:0x%04X...",
                     vid_pid_list[i][0], vid_pid_list[i][1]);
            err = cdc_acm_host_open(vid_pid_list[i][0], vid_pid_list[i][1], 0, &dev_config, &hlk_device);
            if (err == ESP_OK) {
                ESP_LOGI(TAG, "Successfully opened CDC device 0x%04X:0x%04X",
                         vid_pid_list[i][0], vid_pid_list[i][1]);
                break;
            }
        }

        if (err != ESP_OK) {
            ESP_LOGI(TAG, "No CDC device found, retrying in 3 seconds...");
            vTaskDelay(pdMS_TO_TICKS(3000));
            continue;
        }

        ESP_LOGI(TAG, "CDC device connected successfully!");

        // Print device descriptor
        cdc_acm_host_desc_print(hlk_device);

        // Give device time to stabilize after connection
        ESP_LOGI(TAG, "Allowing device to stabilize...");
        vTaskDelay(pdMS_TO_TICKS(1000));

        // Try to configure line coding for HLK-TX510: 115200 8N1
        ESP_LOGI(TAG, "Attempting to configure line coding for HLK-TX510 (115200 8N1)");
        cdc_acm_line_coding_t line_coding;

        // Try to get current line coding (optional - some devices don't support this)
        esp_err_t ret = cdc_acm_host_line_coding_get(hlk_device, &line_coding);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Current Line Coding: Rate: %"PRIu32", Stop bits: %"PRIu8", Parity: %"PRIu8", Databits: %"PRIu8"",
                     line_coding.dwDTERate, line_coding.bCharFormat, line_coding.bParityType, line_coding.bDataBits);
        } else {
            ESP_LOGW(TAG, "Device doesn't support line coding get: %s (this is normal for some USB-serial converters)", esp_err_to_name(ret));
            // Initialize with default values
            line_coding.dwDTERate = HLK_TX510_BAUDRATE;
            line_coding.bDataBits = 8;
            line_coding.bParityType = 0;
            line_coding.bCharFormat = 0;
        }

        // Try to set HLK-TX510 line coding: 115200 8N1 (optional)
        line_coding.dwDTERate = HLK_TX510_BAUDRATE;  // 115200 bps
        line_coding.bDataBits = 8;                   // 8 data bits
        line_coding.bParityType = 0;                 // No parity
        line_coding.bCharFormat = 0;                 // 1 stop bit

        ret = cdc_acm_host_line_coding_set(hlk_device, &line_coding);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Line coding set successfully to 115200 8N1");
        } else {
            ESP_LOGW(TAG, "Device doesn't support line coding set: %s (this is normal for some USB-serial converters)", esp_err_to_name(ret));
            ESP_LOGI(TAG, "Assuming device is already configured for 115200 8N1");
        }

        // Try to verify line coding was set (optional)
        ret = cdc_acm_host_line_coding_get(hlk_device, &line_coding);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Verified Line Coding: Rate: %"PRIu32", Stop bits: %"PRIu8", Parity: %"PRIu8", Databits: %"PRIu8"",
                     line_coding.dwDTERate, line_coding.bCharFormat, line_coding.bParityType, line_coding.bDataBits);
        } else {
            ESP_LOGI(TAG, "Cannot verify line coding, but proceeding with communication test");
        }

        // Try to set control line state (DTR=true, RTS=false) - optional
        ret = cdc_acm_host_set_control_line_state(hlk_device, true, false);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Control line state set successfully (DTR=true, RTS=false)");
        } else {
            ESP_LOGW(TAG, "Device doesn't support control line state: %s (this is normal for some USB-serial converters)", esp_err_to_name(ret));
        }

        // Give device additional time to initialize
        ESP_LOGI(TAG, "Final device initialization...");
        vTaskDelay(pdMS_TO_TICKS(1000));

        // Test basic connectivity first
        ESP_LOGI(TAG, "Testing basic connectivity with HLK-TX510...");
        ESP_LOGI(TAG, "Sending initial display ON command as connectivity test...");
        uint8_t test_data = 0x01;
        ret = hlk_send_command(HLK_CMD_DISPLAY_ON, &test_data, 1);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Initial command sent successfully - device appears to be responding");
        } else {
            ESP_LOGE(TAG, "Failed to send initial command: %s", esp_err_to_name(ret));
        }

        vTaskDelay(pdMS_TO_TICKS(1000)); // Wait for response

        // Start interactive test task
        ESP_LOGI(TAG, "Starting HLK-TX510 interactive test...");
        BaseType_t test_task_created = xTaskCreate(interactive_test_task, "hlk_test", 4096, NULL, 5, NULL);
        if (test_task_created != pdTRUE) {
            ESP_LOGE(TAG, "Failed to create interactive test task");
        } else {
            ESP_LOGI(TAG, "Interactive test task started successfully");
        }

        // Wait for device disconnection and start over
        ESP_LOGI(TAG, "HLK-TX510 ready! Device will be monitored for disconnection...");
        xSemaphoreTake(device_disconnected_sem, portMAX_DELAY);
        ESP_LOGI(TAG, "Device disconnected, restarting...");
    }
}
