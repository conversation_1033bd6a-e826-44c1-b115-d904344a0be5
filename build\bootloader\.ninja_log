# ninja log v6
21	239	7716537903745754	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	c8b4a66f1277fcc5
23	250	7716537903745754	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	7f1ee2b6d63d2ef9
36	261	7716537903913027	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	9d941ab878637b91
40	272	7716537903933096	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	9dcc6560afee637f
17	296	7716537903708177	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	4a931056c44d47d
32	410	7716537903872881	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	6bcf8eb80c1e527c
26	415	7716537903804242	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	ba2a785b13191bbf
48	419	7716537904025892	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	e0ac3dafaff0713e
14	425	7716537903688141	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	a839f22669e6da4c
10	429	7716537903645343	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	adffa0b0488cdb61
59	437	7716537904144628	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	7bd6355713a2c9ce
30	441	7716537903839314	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	a9c85c663a8f7d9f
69	445	7716537904234646	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	a4981b1b67690336
79	489	7716537904337611	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	707fd7e632d8a6b4
74	493	7716537904274814	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	7c0addae307be96e
93	501	7716537904483037	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	ba71986066d9b0c7
53	553	7716537904067673	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	e0fadc482096014f
66	571	7716537904212243	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	84514fbb8dd1d616
45	590	7716537903985463	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7ff537252f17a38d
63	595	7716537904172124	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	30367d84022be45
272	606	7716537906265291	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	9d7304283c183965
84	611	7716537904377725	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	a5da24bc90570417
98	616	7716537904523185	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	cca43942f8badb46
240	680	7716537905949428	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	987cc72b9d99cfea
250	728	7716537906049755	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	dee97e2a8216fa91
262	767	7716537906167578	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	6a9cea7c086c1e42
296	771	7716537906508170	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	d1e0c755e3d549c3
411	874	7716537907648953	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	1f70bfb0f209c8eb
415	890	7716537907709132	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	3b6dc4dcefa78ddc
437	924	7716537907905993	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	20326be85cbb72ad
445	928	7716537907999646	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	fdfa7a2cef250d54
420	943	7716537907749254	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	ce12e103ba03339
425	947	7716537907805634	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	5663ed554326fa71
501	955	7716537908568296	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	1556970ea057ce01
590	974	7716537909453341	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	97d6afef57c4e71c
553	983	7716537909081990	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	36510da260f03a38
429	994	7716537907845770	esp-idf/log/liblog.a	3b9f1c9f5f610697
442	1002	7716537907963573	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	7bbc5cffa63a2927
489	1055	7716537908431366	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	5b9de40e94495b2f
606	1067	7716537909603448	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	ac9de5f0f8f72990
611	1070	7716537909663616	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	8f38be60a83d6e45
571	1084	7716537909254225	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	9ff668b729acaf9a
595	1099	7716537909493462	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	7a36a46c12f9b680
771	1102	7716537911253876	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	79eb46ba416d8e7b
493	1164	7716537908486292	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	6f9ddd159d028cc
680	1211	7716537910355586	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	f1f0b4893e41ea9f
943	1254	7716537912978142	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	50d690c52b5dff49
728	1267	7716537910832041	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	4702acabfe281665
924	1281	7716537912794984	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	195eda0fa31654d6
616	1307	7716537909703726	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	5c7c318ff12aa8bb
928	1312	7716537912831688	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	77c265ccffd5c627
1002	1372	7716537913565446	esp-idf/esp_app_format/CMakeFiles/__idf_esp_app_format.dir/esp_app_desc.c.obj	2e25f6f88c6d1a61
983	1376	7716537913367237	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	746d37da28df8eb4
947	1383	7716537913022580	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	4b37f89c35834146
1067	1401	7716537914209339	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	b09da78c6317f20f
955	1404	7716537913096668	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	4fa8eda9ee7e1bb9
1084	1440	7716537914393615	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	a6176658db6f7aa
890	1447	7716537912442038	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	af96bbc7c14199ff
1102	1453	7716537914574689	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	922f04a9ebe8fec1
767	1458	7716537911213736	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	7e5681ab95d2eed8
974	1473	7716537913287001	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	99b4375194a8d385
874	1478	7716537912284075	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	ce0dbb78b5965bcc
1056	1483	7716537914099839	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	6e0e7856c1e329d4
1070	1496	7716537914249459	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	45345c8db6a42c2a
1267	1501	7716537916214605	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	f701f5f613d138f0
1254	1524	7716537916084490	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	f5ad28ab0c1d4260
1099	1528	7716537914524521	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	8a9b2ccd8c35a22a
1312	1539	7716537916671655	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	eeb539179713730e
1307	1542	7716537916609687	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	789450adeb207cca
1211	1545	7716537915664375	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	b1fb8b5fcd77155e
994	1559	7716537913485089	esp-idf/esp_rom/libesp_rom.a	7b327b051b7b6c47
1281	1592	7716537916357381	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	99d078934e61e4a3
1372	1603	7716537917279736	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	a3f921d21a326de9
1383	1637	7716537917364989	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	f1dce989bd8a4769
1440	1645	7716537917946926	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	844b63a4191084d8
1404	1649	7716537917594921	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	45cea1a89af4e28c
1401	1650	7716537917544759	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	edff549fecc07e41
1376	1650	7716537917304830	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	231e0e57d5197bfa
1447	1667	7716537918016874	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	b7557759b06b2c7a
1453	1704	7716537918079630	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	94362be501818a86
1502	1712	7716537918560494	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	d6ed5fa40a34061
1496	1717	7716537918512988	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	28c0cecc84ab8975
1473	1717	7716537918278138	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	d7be74a543aa2d39
1483	1717	7716537918372453	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	494c17593c7b3088
1539	1718	7716537918926761	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_periph.c.obj	1418f4ae838e0a59
1528	1722	7716537918837134	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	349ed0d0a67eb703
1478	1727	7716537918318262	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	e1dbee790382ee7d
1542	1733	7716537918966884	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	956da4ca9bd047e1
1524	1735	7716537918794046	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	4a6c2504589ac1ce
1637	1736	7716537920639032	project_elf_src_esp32s3.c	7050c25a0bdbab47
1637	1736	7716537920639032	C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/project_elf_src_esp32s3.c	7050c25a0bdbab47
1458	1745	7716537918117457	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	f1fbc7fbcb4af657
1592	1755	7716537919456216	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	5fdcaba1b925636c
1545	1759	7716537918997423	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	5a78035dd797b83a
1603	1773	7716537919577611	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	2ec272cbbd1a43f9
1559	1784	7716537919139288	esp-idf/esp_common/libesp_common.a	782d51f947e57276
1736	1807	7716537920917787	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	acc392ec71d0bd92
1645	1808	7716537919993079	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	e8a3602effb3f361
1784	1874	7716537921395436	esp-idf/esp_hw_support/libesp_hw_support.a	8edcfb22dc7b4cc0
1874	1928	7716537922275561	esp-idf/esp_system/libesp_system.a	c794bdd70bf1b513
1164	1980	7716537915187633	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	ba386de49860b939
1928	2006	7716537922820160	esp-idf/efuse/libefuse.a	150d83bbb3c152a5
2006	2076	7716537923561993	esp-idf/bootloader_support/libbootloader_support.a	6f3525d20755ab95
2076	2130	7716537924258784	esp-idf/esp_app_format/libesp_app_format.a	19be2ab2e8aaa78b
2130	2199	7716537924811374	esp-idf/spi_flash/libspi_flash.a	2be4d599f35ab9a0
2199	2269	7716537925521686	esp-idf/hal/libhal.a	e29552dd53ee218f
2269	2331	7716537926201178	esp-idf/micro-ecc/libmicro-ecc.a	4494ba0ac35bfaa8
2331	2413	7716537926863952	esp-idf/soc/libsoc.a	efe11eb9398ad23
2413	2474	7716537927662832	esp-idf/xtensa/libxtensa.a	3388b978dc117c4f
2474	2532	7716537928289921	esp-idf/main/libmain.a	8e833d15ddc2f1fd
2532	2678	7716537928869241	bootloader.elf	6185897ec9442b65
2678	2970	7716537933177532	.bin_timestamp	6351b2f69cb94362
2678	2970	7716537933177532	C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/.bin_timestamp	6351b2f69cb94362
2970	3054	7716537933228205	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	7f37bf0c0ef6e786
2970	3054	7716537933228205	C:/Users/<USER>/vscode/projects-lvgl/cdc_acm_host/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	7f37bf0c0ef6e786
