| Supported Targets | ESP32-S3 |
| ----------------- | -------- |

# HLK-TX510 Face Recognition Controller

This project enables the WaveShare ESP32-S3-Touch-LCD-4.3 to control an HLK-TX510 face recognition module via USB serial communications.

## Hardware Setup

### Required Hardware
- **WaveShare ESP32-S3-Touch-LCD-4.3** development board
- **HLK-TX510** face recognition module
- USB-C cable for connecting ESP32-S3 "USB" port to HLK-TX510 USB-C command port
- USB-C cable for connecting ESP32-S3 "UART" port to PC for programming/monitoring

### Connections
1. **Programming Connection**: Connect PC to ESP32-S3 "UART" port for flashing and monitoring
2. **HLK-TX510 Connection**: Connect ESP32-S3 "USB" port directly to HLK-TX510 USB-C command port

### Communication Settings
- **Baud Rate**: 115200
- **Data Format**: 8N1 (8 data bits, no parity, 1 stop bit)
- **Protocol**: HLK-TX510 proprietary protocol with EF AA sync word

## HLK-TX510 Commands Implemented

| Command | Hex Code | Description |
|---------|----------|-------------|
| Display OFF | `EF AA C1 00 00 00 01 00 C2` | Turn off HLK-TX510 display |
| Display ON | `EF AA C1 00 00 00 01 01 C3` | Turn on HLK-TX510 display |
| Start Recognition | `EF AA 12 00 00 00 00 12` | Begin face recognition |
| Register Face | `EF AA 13 00 00 00 00 13` | Register a new face |

## Build and Flash

1. Set up ESP-IDF environment (v5.1.6 or compatible)
2. Build and flash the project:

```bash
idf.py build
idf.py -p PORT flash monitor
```

Replace PORT with your ESP32-S3 UART port (e.g., COM3 on Windows).

## Usage

1. **Connect Hardware**: Wire ESP32-S3 to HLK-TX510 as described above
2. **Power On**: Both devices should power up and establish USB communication
3. **Monitor Output**: Watch the serial monitor for connection status and command results
4. **Automatic Testing**: The program automatically cycles through test commands every 30 seconds

## Expected Output

```
I (256) HLK-TX510: Installing USB Host
I (256) HLK-TX510: Installing CDC-ACM driver
I (256) HLK-TX510: Waiting for HLK-TX510 device connection...
I (256) HLK-TX510: Successfully opened CDC device 0x1A86:0x7523
I (256) HLK-TX510: Configuring line coding for HLK-TX510 (115200 8N1)
I (256) HLK-TX510: Starting HLK-TX510 interactive test...
I (256) HLK-TX510: Sending display OFF command
I (256) HLK-TX510: HLK-TX510 Response received (9 bytes):
I (256) HLK-TX510: Display command acknowledged
```

## Troubleshooting

### Device Not Found
- Verify USB-C cable connections
- Check that HLK-TX510 is powered on
- Try different USB-C cables
- Ensure ESP32-S3 USB host is properly configured

### Communication Errors
- Verify baud rate is set to 115200
- Check cable integrity
- Ensure proper power supply to both devices

### No Response from HLK-TX510
- Verify command format matches HLK-TX510 protocol
- Check checksum calculation
- Ensure proper timing between commands

## References

- [HLK-TX510 User Manual](https://github.com/blakadder/HLK-TX510/blob/main/HLK-TX510_User%20Manual_V1.0.pdf)
- [HLK-TX510 Review and Documentation](https://blakadder.com/hlk-tx510/)
- [ESP-IDF USB Host CDC-ACM Documentation](https://docs.espressif.com/projects/esp-idf/en/latest/esp32s3/api-reference/peripherals/usb_host.html)
